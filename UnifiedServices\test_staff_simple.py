#!/usr/bin/env python
"""
Simple test for Staff Interface functionality
"""

import os
import sys
import django
from django.test import Client
from django.contrib.auth import get_user_model
from django.urls import reverse

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UnifiedServices.settings')
django.setup()

from appointments.models import *

def test_staff_simple():
    """Simple test for staff interface"""
    print("🧪 Simple Staff Interface Test")
    print("=" * 40)
    
    client = Client()
    User = get_user_model()
    
    try:
        # Get staff user
        staff_user = User.objects.get(username='registrar1')
        print(f"✅ Staff user: {staff_user.get_full_name()}")
        print(f"   Department: {staff_user.userprofile.department}")
        
        # Login
        login_success = client.login(username='registrar1', password='password123')
        if login_success:
            print("✅ Staff login successful")
        else:
            print("❌ Staff login failed")
            return False
        
        # Test staff dashboard
        response = client.get(reverse('staff_dashboard'))
        print(f"✅ Staff dashboard: {response.status_code}")
        
        # Test staff appointment list
        response = client.get(reverse('staff_appointment_list'))
        print(f"✅ Staff appointment list: {response.status_code}")
        
        # Get appointments for this department
        dept_appointments = Appointment.objects.filter(
            service__department=staff_user.userprofile.department
        )
        print(f"✅ Found {dept_appointments.count()} appointments for department")
        
        if dept_appointments.exists():
            test_appointment = dept_appointments.first()
            print(f"✅ Test appointment: {test_appointment.appointment_id}")
            
            # Test appointment detail
            detail_url = reverse('staff_appointment_detail', kwargs={'pk': test_appointment.appointment_id})
            print(f"   Detail URL: {detail_url}")
            
            response = client.get(detail_url)
            print(f"✅ Staff appointment detail: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ All tests passed!")
                return True
            else:
                print(f"❌ Detail view failed: {response.status_code}")
                return False
        else:
            print("⚠️  No appointments found for testing")
            return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_staff_simple()
    sys.exit(0 if success else 1)
