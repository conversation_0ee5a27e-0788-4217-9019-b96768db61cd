<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JHCSC Unified Student Services - Streamline Your Academic Journey</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        emerald: {
                            50: '#ecfdf5',
                            100: '#d1fae5',
                            200: '#a7f3d0',
                            300: '#6ee7b7',
                            400: '#34d399',
                            500: '#10b981',
                            600: '#059669',
                            700: '#047857',
                            800: '#065f46',
                            900: '#064e3b',
                            950: '#022c22'
                        },
                        gold: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f'
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.6s ease-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        }
                    }
                }
            }
        }
    </script>

    <style>
        [x-cloak] { display: none !important; }

        /* Clean Emerald & Gold Theme */
        .hero-gradient {
            background: linear-gradient(135deg, #064e3b 0%, #047857 50%, #059669 100%);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-4px);
        }

        .text-gradient {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="h-full antialiased" x-data="landingPage()" x-cloak>
    <!-- Navigation -->
    <nav class="fixed w-full z-50 transition-all duration-300"
         :class="scrolled ? 'bg-white/95 backdrop-blur-md shadow-lg' : 'bg-emerald-900/95 backdrop-blur-md'">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.26 10.147a60.436 60.436 0 00-.491 6.347A48.627 48.627 0 0112 20.904a48.627 48.627 0 018.232-4.41 60.46 60.46 0 00-.491-6.347m-15.482 0a50.57 50.57 0 00-2.658-.813A59.905 59.905 0 0112 3.493a59.902 59.902 0 0110.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.697 50.697 0 0112 13.489a50.702 50.702 0 017.74-3.342M6.75 15a.75.75 0 100-********* 0 000 1.5zm0 0v-3.675A55.378 55.378 0 0112 8.443a55.381 55.381 0 015.25 2.882V15" />
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold" :class="scrolled ? 'text-gray-900' : 'text-white'">JHCSC</h1>
                        <p class="text-sm font-medium" :class="scrolled ? 'text-gray-600' : 'text-gold-300'">Student Services</p>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#features" class="font-medium transition-colors duration-200" :class="scrolled ? 'text-gray-700 hover:text-emerald-600' : 'text-white hover:text-gold-300'">Features</a>
                    <a href="#services" class="font-medium transition-colors duration-200" :class="scrolled ? 'text-gray-700 hover:text-emerald-600' : 'text-white hover:text-gold-300'">Services</a>
                    <a href="#departments" class="font-medium transition-colors duration-200" :class="scrolled ? 'text-gray-700 hover:text-emerald-600' : 'text-white hover:text-gold-300'">Departments</a>
                    <a href="#contact" class="font-medium transition-colors duration-200" :class="scrolled ? 'text-gray-700 hover:text-emerald-600' : 'text-white hover:text-gold-300'">Contact</a>
                </div>

                <!-- Auth Buttons -->
                <div class="flex items-center space-x-4">
                    <a href="{% url 'login' %}"
                       class="px-4 py-2 font-medium rounded-lg transition-colors duration-200"
                       :class="scrolled ? 'text-gray-700 hover:bg-gray-100' : 'text-white hover:bg-white/10'">
                        Sign In
                    </a>
                    <a href="{% url 'dashboard' %}"
                       class="px-6 py-2 bg-gradient-to-r from-gold-500 to-gold-600 text-white font-semibold rounded-lg hover:from-gold-600 hover:to-gold-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        Get Started
                    </a>

                    <!-- Mobile menu button -->
                    <button @click="mobileMenuOpen = !mobileMenuOpen" class="md:hidden p-2 rounded-lg transition-colors duration-200"
                            :class="scrolled ? 'text-gray-700 hover:bg-gray-100' : 'text-white hover:bg-white/10'">
                        <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div x-show="mobileMenuOpen" x-transition class="md:hidden bg-white border-t shadow-lg">
            <div class="px-4 py-4 space-y-3">
                <a href="#features" class="block text-gray-700 hover:text-emerald-600 font-medium">Features</a>
                <a href="#services" class="block text-gray-700 hover:text-emerald-600 font-medium">Services</a>
                <a href="#departments" class="block text-gray-700 hover:text-emerald-600 font-medium">Departments</a>
                <a href="#contact" class="block text-gray-700 hover:text-emerald-600 font-medium">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center hero-gradient">
        <div class="absolute inset-0 bg-black/20"></div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="animate-fade-in">
                <h1 class="text-5xl md:text-7xl font-bold text-white mb-8 leading-tight">
                    Streamline Your
                    <span class="text-gradient block mt-2">
                        Academic Journey
                    </span>
                </h1>
                <p class="text-xl md:text-2xl text-emerald-100 mb-12 max-w-4xl mx-auto leading-relaxed">
                    Experience the future of student services at J.H. Cerilles State College.
                    Book appointments, track progress, and access all services from one unified platform.
                </p>
            </div>

            <div class="animate-slide-up flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
                <a href="{% url 'dashboard' %}"
                   class="px-8 py-4 bg-gradient-to-r from-gold-500 to-gold-600 text-white font-semibold rounded-lg hover:from-gold-600 hover:to-gold-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-3">
                    <span>Start Your Journey</span>
                    <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                </a>
                <button @click="showDemo = true"
                        class="px-8 py-4 border-2 border-gold-400 text-white font-semibold rounded-lg hover:bg-gold-400 hover:text-emerald-900 transition-all duration-200 flex items-center space-x-3">
                    <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>Watch Demo</span>
                </button>
            </div>

            <!-- Stats -->
            <div class="animate-slide-up grid grid-cols-2 md:grid-cols-4 gap-6 max-w-5xl mx-auto">
                <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center card-hover">
                    <div class="text-3xl md:text-4xl font-bold text-gold-400 mb-2">{{ total_departments }}+</div>
                    <div class="text-emerald-100 font-medium">Departments</div>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center card-hover">
                    <div class="text-3xl md:text-4xl font-bold text-gold-400 mb-2">{{ total_services }}+</div>
                    <div class="text-emerald-100 font-medium">Services</div>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center card-hover">
                    <div class="text-3xl md:text-4xl font-bold text-gold-400 mb-2">{{ total_appointments }}+</div>
                    <div class="text-emerald-100 font-medium">Appointments</div>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center card-hover">
                    <div class="text-3xl md:text-4xl font-bold text-gold-400 mb-2">{{ active_students }}+</div>
                    <div class="text-emerald-100 font-medium">Active Students</div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <svg class="w-6 h-6 text-white/70" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Powerful Features for Modern Students</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Our comprehensive platform brings together all student services in one intuitive, mobile-first experience.
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <!-- Feature Showcase -->
                <div class="relative">
                    <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-200">
                        <!-- Feature Tabs -->
                        <div class="flex space-x-2 mb-8">
                            <button @click="currentFeature = 0"
                                    :class="currentFeature === 0 ? 'bg-emerald-600 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'"
                                    class="px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                                Appointments
                            </button>
                            <button @click="currentFeature = 1"
                                    :class="currentFeature === 1 ? 'bg-emerald-600 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'"
                                    class="px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                                QR Codes
                            </button>
                            <button @click="currentFeature = 2"
                                    :class="currentFeature === 2 ? 'bg-emerald-600 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'"
                                    class="px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                                Tracking
                            </button>
                        </div>

                        <!-- Feature Content -->
                        <div class="relative h-64">
                            <!-- Appointment Feature -->
                            <div x-show="currentFeature === 0" x-transition class="absolute inset-0">
                                <div class="bg-emerald-50 rounded-xl p-6 border border-emerald-200">
                                    <div class="flex items-center space-x-3 mb-4">
                                        <div class="w-10 h-10 bg-emerald-600 rounded-lg flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                        <h3 class="text-lg font-semibold text-gray-900">Smart Appointment Booking</h3>
                                    </div>
                                    <p class="text-gray-600 mb-4">Book appointments across all departments with real-time availability and automatic confirmations.</p>
                                    <div class="space-y-2">
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                                            <span class="text-sm text-gray-600">Real-time scheduling</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                                            <span class="text-sm text-gray-600">Automatic reminders</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                                            <span class="text-sm text-gray-600">Multi-department support</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- QR Code Feature -->
                            <div x-show="currentFeature === 1" x-transition class="absolute inset-0">
                                <div class="bg-gold-50 rounded-xl p-6 border border-gold-200">
                                    <div class="flex items-center space-x-3 mb-4">
                                        <div class="w-10 h-10 bg-gold-600 rounded-lg flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
                                            </svg>
                                        </div>
                                        <h3 class="text-lg font-semibold text-gray-900">Secure QR Codes</h3>
                                    </div>
                                    <p class="text-gray-600 mb-4">Generate and scan secure QR codes for document pickup and service verification.</p>
                                    <div class="space-y-2">
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 bg-gold-500 rounded-full"></div>
                                            <span class="text-sm text-gray-600">HMAC verification</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 bg-gold-500 rounded-full"></div>
                                            <span class="text-sm text-gray-600">Mobile scanning</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 bg-gold-500 rounded-full"></div>
                                            <span class="text-sm text-gray-600">Instant validation</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Tracking Feature -->
                            <div x-show="currentFeature === 2" x-transition class="absolute inset-0">
                                <div class="bg-gradient-to-br from-emerald-50 to-gold-50 rounded-xl p-6 border border-emerald-200">
                                    <div class="flex items-center space-x-3 mb-4">
                                        <div class="w-10 h-10 bg-gradient-to-r from-emerald-600 to-gold-600 rounded-lg flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                            </svg>
                                        </div>
                                        <h3 class="text-lg font-semibold text-gray-900">Real-time Progress Tracking</h3>
                                    </div>
                                    <p class="text-gray-600 mb-4">Track your requests and documents through every step of the process with live updates.</p>
                                    <div class="space-y-2">
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                                            <span class="text-sm text-gray-600">Live status updates</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                                            <span class="text-sm text-gray-600">Progress notifications</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                                            <span class="text-sm text-gray-600">Completion alerts</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Feature List -->
                <div class="space-y-6">
                    <div class="flex items-start space-x-4">
                        <div class="w-10 h-10 bg-emerald-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Mobile-First Design</h3>
                            <p class="text-gray-600">Optimized for smartphones and tablets with responsive design that works perfectly on any device.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="w-10 h-10 bg-gold-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Secure & Private</h3>
                            <p class="text-gray-600">Enterprise-grade security with encrypted data transmission and comprehensive audit logging.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="w-10 h-10 bg-emerald-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Lightning Fast</h3>
                            <p class="text-gray-600">Built with modern web technologies for instant loading and smooth interactions.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="w-10 h-10 bg-gold-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Multi-Role Support</h3>
                            <p class="text-gray-600">Tailored interfaces for students, staff, and administrators with role-based access control.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Popular Student Services</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Access all the services you need for your academic journey, from enrollment to graduation.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {% for service in popular_services %}
                <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 card-hover">
                    <div class="w-12 h-12 bg-emerald-600 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ service.name }}</h3>
                    <p class="text-gray-600 mb-6 text-sm leading-relaxed">{{ service.description|truncatewords:15 }}</p>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-emerald-600 font-medium">{{ service.department.name }}</span>
                        <span class="text-gold-600 font-medium">{{ service.processing_time }}</span>
                    </div>
                </div>
                {% empty %}
                <!-- Default services if none exist -->
                <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 card-hover">
                    <div class="w-12 h-12 bg-emerald-600 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Transcript Request</h3>
                    <p class="text-gray-600 mb-6 text-sm leading-relaxed">Official academic transcripts for employment or transfer applications.</p>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-emerald-600 font-medium">Registrar</span>
                        <span class="text-gold-600 font-medium">3-5 days</span>
                    </div>
                </div>
                <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 card-hover">
                    <div class="w-12 h-12 bg-gold-600 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Financial Aid</h3>
                    <p class="text-gray-600 mb-6 text-sm leading-relaxed">Apply for scholarships, grants, and financial assistance programs.</p>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-emerald-600 font-medium">Financial Aid</span>
                        <span class="text-gold-600 font-medium">1-2 weeks</span>
                    </div>
                </div>
                <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 card-hover">
                    <div class="w-12 h-12 bg-gradient-to-r from-emerald-600 to-gold-600 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Course Enrollment</h3>
                    <p class="text-gray-600 mb-6 text-sm leading-relaxed">Register for classes and manage your academic schedule.</p>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-emerald-600 font-medium">Registrar</span>
                        <span class="text-gold-600 font-medium">Same day</span>
                    </div>
                </div>
                <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 card-hover">
                    <div class="w-12 h-12 bg-gold-600 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Student ID</h3>
                    <p class="text-gray-600 mb-6 text-sm leading-relaxed">Request new student ID cards or replacements for lost cards.</p>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-emerald-600 font-medium">Student Affairs</span>
                        <span class="text-gold-600 font-medium">1-2 days</span>
                    </div>
                </div>
                {% endfor %}
            </div>

            <div class="text-center mt-12">
                <a href="{% url 'dashboard' %}"
                   class="inline-flex items-center px-8 py-3 bg-gradient-to-r from-emerald-600 to-gold-600 text-white font-semibold rounded-lg hover:from-emerald-700 hover:to-gold-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                    <span>View All Services</span>
                    <svg class="ml-2 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- Departments Section -->
    <section id="departments" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Academic Departments</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Connect with all academic and administrative departments through our unified platform.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for department in featured_departments %}
                <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 card-hover">
                    <div class="w-12 h-12 bg-emerald-600 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ department.name }}</h3>
                    <p class="text-gray-600 mb-4 text-sm">{{ department.description|truncatewords:20 }}</p>
                    <div class="flex items-center text-sm text-emerald-600 font-medium">
                        <span>{{ department.code }}</span>
                        <span class="mx-2">•</span>
                        <span>{{ department.services.count }} service{{ department.services.count|pluralize }}</span>
                    </div>
                </div>
                {% empty %}
                <!-- Default departments if none exist -->
                <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 card-hover">
                    <div class="w-12 h-12 bg-emerald-600 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Registrar's Office</h3>
                    <p class="text-gray-600 mb-4 text-sm">Academic records, transcripts, enrollment, and graduation services.</p>
                    <div class="flex items-center text-sm text-emerald-600 font-medium">
                        <span>REG</span>
                        <span class="mx-2">•</span>
                        <span>8 services</span>
                    </div>
                </div>
                <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 card-hover">
                    <div class="w-12 h-12 bg-gold-600 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Financial Aid</h3>
                    <p class="text-gray-600 mb-4 text-sm">Scholarships, grants, student loans, and financial assistance programs.</p>
                    <div class="flex items-center text-sm text-emerald-600 font-medium">
                        <span>FIN</span>
                        <span class="mx-2">•</span>
                        <span>6 services</span>
                    </div>
                </div>
                <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 card-hover">
                    <div class="w-12 h-12 bg-gradient-to-r from-emerald-600 to-gold-600 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Student Affairs</h3>
                    <p class="text-gray-600 mb-4 text-sm">Student life, activities, counseling, and support services.</p>
                    <div class="flex items-center text-sm text-emerald-600 font-medium">
                        <span>SA</span>
                        <span class="mx-2">•</span>
                        <span>12 services</span>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 hero-gradient">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-4xl font-bold text-white mb-6">
                Ready to Transform Your Student Experience?
            </h2>
            <p class="text-xl text-emerald-100 mb-8 max-w-3xl mx-auto">
                Join thousands of students who have streamlined their academic journey with our unified platform.
            </p>
            <div class="flex flex-col sm:flex-row gap-6 justify-center">
                <a href="{% url 'dashboard' %}"
                   class="px-8 py-4 bg-white text-emerald-600 font-semibold rounded-lg hover:bg-gray-50 transition-colors duration-200 shadow-lg">
                    Get Started Today
                </a>
                <a href="#contact"
                   class="px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-emerald-600 transition-colors duration-200">
                    Contact Support
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact" class="bg-gray-900 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Logo and Description -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M4.26 10.147a60.436 60.436 0 00-.491 6.347A48.627 48.627 0 0112 20.904a48.627 48.627 0 018.232-4.41 60.46 60.46 0 00-.491-6.347m-15.482 0a50.57 50.57 0 00-2.658-.813A59.905 59.905 0 0112 3.493a59.902 59.902 0 0110.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.697 50.697 0 0112 13.489a50.702 50.702 0 017.74-3.342M6.75 15a.75.75 0 100-********* 0 000 1.5zm0 0v-3.675A55.378 55.378 0 0112 8.443a55.381 55.381 0 015.25 2.882V15" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white">JHCSC Student Services</h3>
                            <p class="text-gold-300 font-medium">Unified Platform</p>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-6 leading-relaxed">
                        Streamlining student services at J.H. Cerilles State College through innovative technology and unified access to all academic and administrative services.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-emerald-400 transition-colors duration-200">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-emerald-400 transition-colors duration-200">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-emerald-400 transition-colors duration-200">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-emerald-400 transition-colors duration-200">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-11.987C24.007 5.367 18.641.001 12.017.001z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold text-white mb-6">Quick Links</h4>
                    <ul class="space-y-3">
                        <li><a href="{% url 'dashboard' %}" class="text-gray-300 hover:text-emerald-400 transition-colors duration-200">Dashboard</a></li>
                        <li><a href="#services" class="text-gray-300 hover:text-emerald-400 transition-colors duration-200">Services</a></li>
                        <li><a href="#departments" class="text-gray-300 hover:text-emerald-400 transition-colors duration-200">Departments</a></li>
                        <li><a href="{% url 'login' %}" class="text-gray-300 hover:text-emerald-400 transition-colors duration-200">Sign In</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-emerald-400 transition-colors duration-200">Help Center</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold text-white mb-6">Contact Us</h4>
                    <div class="space-y-3">
                        <div class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-emerald-400 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <div>
                                <p class="text-gray-300 font-semibold">J.H. Cerilles State College </p>
                                <p class="text-gray-400 text-sm">Caridad, Dumingag, Zamboanga Del Sur</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-emerald-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                            </svg>
                            <p class="text-gray-300">(075) 123-4567</p>
                        </div>
                        <div class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-emerald-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <p class="text-gray-300"><EMAIL></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm">
                        © 2025 J.H. Cerilles State College. All rights reserved.
                    </p>
                    <div class="flex space-x-6 mt-4 md:mt-0">
                        <a href="#" class="text-gray-400 hover:text-emerald-400 text-sm transition-colors duration-200">Privacy Policy</a>
                        <a href="#" class="text-gray-400 hover:text-emerald-400 text-sm transition-colors duration-200">Terms of Service</a>
                        <a href="#" class="text-gray-400 hover:text-emerald-400 text-sm transition-colors duration-200">Accessibility</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Demo Modal -->
    <div x-show="showDemo" x-transition class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity bg-gray-900 bg-opacity-75" @click="showDemo = false"></div>

            <div class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-2xl font-bold text-gray-900">Platform Demo</h3>
                    <button @click="showDemo = false" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                        <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <div class="aspect-video bg-gray-100 rounded-xl flex items-center justify-center mb-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-2">Interactive Demo</h4>
                        <p class="text-gray-600 mb-4">Experience the platform with our interactive demo</p>
                        <a href="{% url 'dashboard' %}"
                           class="inline-flex items-center px-6 py-3 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 transition-colors duration-200">
                            <span>Try Live Demo</span>
                            <svg class="ml-2 w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                            </svg>
                        </a>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center p-4 bg-gray-50 rounded-lg">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <svg class="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <h5 class="font-semibold text-gray-900">Mobile First</h5>
                        <p class="text-sm text-gray-600">Optimized for mobile devices</p>
                    </div>
                    <div class="text-center p-4 bg-gray-50 rounded-lg">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <svg class="w-4 h-4 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <h5 class="font-semibold text-gray-900">Lightning Fast</h5>
                        <p class="text-sm text-gray-600">Instant loading and updates</p>
                    </div>
                    <div class="text-center p-4 bg-gray-50 rounded-lg">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                            <svg class="w-4 h-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                        </div>
                        <h5 class="font-semibold text-gray-900">Secure</h5>
                        <p class="text-sm text-gray-600">Enterprise-grade security</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function landingPage() {
            return {
                scrolled: false,
                mobileMenuOpen: false,
                showDemo: false,
                currentFeature: 0,

                init() {
                    window.addEventListener('scroll', () => {
                        this.scrolled = window.scrollY > 50;
                    });

                    // Auto-rotate features
                    setInterval(() => {
                        this.currentFeature = (this.currentFeature + 1) % 3;
                    }, 5000);

                    // Smooth scrolling for anchor links
                    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                        anchor.addEventListener('click', function (e) {
                            e.preventDefault();
                            const target = document.querySelector(this.getAttribute('href'));
                            if (target) {
                                target.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'start'
                                });
                            }
                        });
                    });
                }
            }
        }
    </script>
</body>
</html>
