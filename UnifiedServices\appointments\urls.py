from django.urls import path
from . import views

urlpatterns = [
    # Landing page
    path('', views.LandingPageView.as_view(), name='landing'),

    # Dashboard redirects
    path('dashboard/', views.dashboard_redirect, name='dashboard'),

    # Role-specific dashboards
    path('student/', views.StudentDashboardView.as_view(), name='student_dashboard'),
    path('staff/', views.OfficeStaffDashboardView.as_view(), name='staff_dashboard'),
    path('administrator/', views.AdminDashboardView.as_view(), name='admin_dashboard'),

    # Administrator management
    path('administrator/appointments/', views.AdminAppointmentListView.as_view(), name='admin_appointment_list'),
    path('administrator/users/', views.AdminUserListView.as_view(), name='admin_user_list'),
    path('administrator/departments/', views.AdminDepartmentListView.as_view(), name='admin_department_list'),
    path('administrator/reports/', views.AdminSystemReportsView.as_view(), name='admin_reports'),
    path('administrator/security/', views.AdminSecurityDashboardView.as_view(), name='admin_security_dashboard'),
    path('administrator/audit-logs/', views.AdminAuditLogsView.as_view(), name='admin_audit_logs'),

    # Security management
    path('security/resolve-event/<int:event_id>/', views.resolve_security_event, name='resolve_security_event'),
    path('security/resolve-all-low/', views.resolve_all_low_events, name='resolve_all_low_events'),
    path('security/export-report/', views.export_security_report, name='export_security_report'),

    # Student appointment management
    path('appointments/', views.AppointmentListView.as_view(), name='appointment_list'),
    path('appointments/create/', views.AppointmentCreateView.as_view(), name='appointment_create'),
    path('appointments/<int:pk>/', views.AppointmentDetailView.as_view(), name='appointment_detail'),

    # Service browsing
    path('services/', views.ServiceListView.as_view(), name='service_list'),
    path('services/<int:pk>/', views.ServiceDetailView.as_view(), name='service_detail'),

    # File upload
    path('upload/', views.FileUploadView.as_view(), name='file_upload'),

    # Staff appointment management
    path('staff/appointments/', views.StaffAppointmentListView.as_view(), name='staff_appointment_list'),
    path('staff/appointments/<str:pk>/', views.StaffAppointmentDetailView.as_view(), name='staff_appointment_detail'),
    path('staff/appointments/<str:appointment_id>/requirements/<int:requirement_id>/toggle/',
         views.toggle_requirement_completion, name='toggle_requirement_completion'),
    path('staff/appointments/<str:appointment_id>/status/',
         views.update_appointment_status, name='update_appointment_status'),
    path('staff/appointments/<str:appointment_id>/requirements/<int:requirement_id>/note/',
         views.add_requirement_note, name='add_requirement_note'),

    # QR Code management
    path('qr/scanner/', views.QRScannerView.as_view(), name='qr_scanner'),
    path('qr/generate/<str:appointment_id>/', views.generate_qr_code, name='generate_qr_code'),
    path('qr/download/<str:appointment_id>/', views.download_qr_code, name='download_qr_code'),
    path('qr/validate/', views.validate_qr_code, name='validate_qr_code'),
    path('qr/claim/<str:appointment_id>/', views.claim_appointment, name='claim_appointment'),

    # Unauthorized access
    path('unauthorized/', views.UnauthorizedView.as_view(), name='unauthorized'),
]
