from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from django.utils import timezone
from .models import (
    UserProfile, Department, Service, ServiceRequirement,
    Appointment, AppointmentRequirement, AuditLog, SecurityEvent, FileAccessLog
)


class UserProfileInline(admin.StackedInline):
    model = UserProfile
    can_delete = False
    verbose_name_plural = 'Profile'


class UserAdmin(BaseUserAdmin):
    inlines = (UserProfileInline,)
    list_display = ('username', 'email', 'first_name', 'last_name', 'get_role', 'is_staff')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'userprofile__role')

    def get_role(self, obj):
        return obj.userprofile.get_role_display() if hasattr(obj, 'userprofile') else 'No Profile'
    get_role.short_description = 'Role'


# Re-register UserAdmin
admin.site.unregister(User)
admin.site.register(User, UserAdmin)


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'role', 'department', 'student_id', 'phone_number')
    list_filter = ('role', 'department')
    search_fields = ('user__username', 'user__first_name', 'user__last_name', 'student_id')


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'is_active', 'created_at')
    list_filter = ('is_active',)
    search_fields = ('name', 'code')
    prepopulated_fields = {'code': ('name',)}


class ServiceRequirementInline(admin.TabularInline):
    model = ServiceRequirement
    extra = 1
    ordering = ('order', 'name')


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    list_display = ('name', 'department', 'processing_time', 'is_active', 'created_at')
    list_filter = ('department', 'is_active')
    search_fields = ('name', 'department__name')
    inlines = [ServiceRequirementInline]


@admin.register(ServiceRequirement)
class ServiceRequirementAdmin(admin.ModelAdmin):
    list_display = ('name', 'service', 'is_required', 'requires_upload', 'order')
    list_filter = ('is_required', 'requires_upload', 'service__department')
    search_fields = ('name', 'service__name')


class AppointmentRequirementInline(admin.TabularInline):
    model = AppointmentRequirement
    extra = 0
    readonly_fields = ('requirement', 'is_completed', 'completed_by', 'completed_at')


@admin.register(Appointment)
class AppointmentAdmin(admin.ModelAdmin):
    list_display = ('appointment_id', 'student', 'service', 'status', 'created_at', 'updated_at')
    list_filter = ('status', 'service__department', 'created_at')
    search_fields = ('appointment_id', 'student__username', 'student__first_name', 'student__last_name')
    readonly_fields = ('appointment_id', 'qr_hash', 'created_at', 'updated_at')
    inlines = [AppointmentRequirementInline]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('student', 'service', 'service__department')


@admin.register(AppointmentRequirement)
class AppointmentRequirementAdmin(admin.ModelAdmin):
    list_display = ('appointment', 'requirement', 'is_completed', 'completed_by', 'completed_at')
    list_filter = ('is_completed', 'requirement__service__department')
    search_fields = ('appointment__appointment_id', 'requirement__name')
    readonly_fields = ('completed_at',)


@admin.register(AuditLog)
class AuditLogAdmin(admin.ModelAdmin):
    list_display = ('timestamp', 'user', 'action', 'description', 'ip_address', 'request_method')
    list_filter = ('action', 'timestamp', 'request_method')
    search_fields = ('user__username', 'description', 'ip_address', 'request_path')
    readonly_fields = ('timestamp',)
    date_hierarchy = 'timestamp'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


@admin.register(SecurityEvent)
class SecurityEventAdmin(admin.ModelAdmin):
    list_display = ('timestamp', 'event_type', 'severity', 'user', 'ip_address', 'resolved')
    list_filter = ('event_type', 'severity', 'resolved', 'timestamp')
    search_fields = ('user__username', 'description', 'ip_address')
    readonly_fields = ('timestamp',)
    date_hierarchy = 'timestamp'
    actions = ['mark_resolved']

    def mark_resolved(self, request, queryset):
        queryset.update(resolved=True, resolved_by=request.user, resolved_at=timezone.now())
        self.message_user(request, f"{queryset.count()} security events marked as resolved.")
    mark_resolved.short_description = "Mark selected events as resolved"

    def has_add_permission(self, request):
        return False


@admin.register(FileAccessLog)
class FileAccessLogAdmin(admin.ModelAdmin):
    list_display = ('timestamp', 'user', 'access_type', 'file_name', 'success', 'ip_address')
    list_filter = ('access_type', 'success', 'timestamp')
    search_fields = ('user__username', 'file_name', 'file_path', 'ip_address')
    readonly_fields = ('timestamp',)
    date_hierarchy = 'timestamp'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False
