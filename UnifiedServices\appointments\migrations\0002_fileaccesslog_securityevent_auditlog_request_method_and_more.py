# Generated by Django 4.2.17 on 2025-07-31 06:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('appointments', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FileAccessLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_path', models.CharField(max_length=500)),
                ('file_name', models.CharField(max_length=255)),
                ('access_type', models.CharField(choices=[('upload', 'File Upload'), ('download', 'File Download'), ('view', 'File View'), ('delete', 'File Delete'), ('unauthorized', 'Unauthorized Access')], max_length=20)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('success', models.BooleanField(default=True)),
                ('error_message', models.TextField(blank=True)),
                ('file_size', models.BigIntegerField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'File Access Log',
                'verbose_name_plural': 'File Access Logs',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='SecurityEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('failed_login', 'Failed Login'), ('multiple_failed_logins', 'Multiple Failed Logins'), ('suspicious_activity', 'Suspicious Activity'), ('unauthorized_access', 'Unauthorized Access Attempt'), ('file_access_violation', 'File Access Violation'), ('qr_tampering', 'QR Code Tampering'), ('session_hijacking', 'Potential Session Hijacking'), ('brute_force', 'Brute Force Attack'), ('sql_injection', 'SQL Injection Attempt'), ('xss_attempt', 'XSS Attempt')], max_length=30)),
                ('severity', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium', max_length=10)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('description', models.TextField()),
                ('request_path', models.CharField(blank=True, max_length=500)),
                ('request_data', models.TextField(blank=True, help_text='JSON formatted request data')),
                ('resolved', models.BooleanField(default=False)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Security Event',
                'verbose_name_plural': 'Security Events',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.AddField(
            model_name='auditlog',
            name='request_method',
            field=models.CharField(blank=True, max_length=10),
        ),
        migrations.AddField(
            model_name='auditlog',
            name='request_path',
            field=models.CharField(blank=True, max_length=500),
        ),
        migrations.AddField(
            model_name='auditlog',
            name='response_status',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='auditlog',
            name='session_key',
            field=models.CharField(blank=True, max_length=40),
        ),
        migrations.AlterField(
            model_name='auditlog',
            name='action',
            field=models.CharField(choices=[('login', 'User Login'), ('logout', 'User Logout'), ('login_failed', 'Failed Login Attempt'), ('password_changed', 'Password Changed'), ('appointment_created', 'Appointment Created'), ('appointment_updated', 'Appointment Updated'), ('appointment_viewed', 'Appointment Viewed'), ('appointment_deleted', 'Appointment Deleted'), ('requirement_completed', 'Requirement Completed'), ('requirement_uncompleted', 'Requirement Uncompleted'), ('file_uploaded', 'File Uploaded'), ('file_downloaded', 'File Downloaded'), ('file_deleted', 'File Deleted'), ('qr_generated', 'QR Code Generated'), ('qr_scanned', 'QR Code Scanned'), ('qr_verified', 'QR Code Verified'), ('document_claimed', 'Document Claimed'), ('user_created', 'User Created'), ('user_updated', 'User Updated'), ('user_deleted', 'User Deleted'), ('role_changed', 'User Role Changed'), ('permission_denied', 'Permission Denied'), ('admin_access', 'Admin Panel Access'), ('data_export', 'Data Export'), ('system_error', 'System Error'), ('security_violation', 'Security Violation')], max_length=30),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['user', 'timestamp'], name='appointment_user_id_37cfb9_idx'),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['action', 'timestamp'], name='appointment_action_18ec9c_idx'),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['ip_address', 'timestamp'], name='appointment_ip_addr_8d812f_idx'),
        ),
        migrations.AddField(
            model_name='securityevent',
            name='resolved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_security_events', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='securityevent',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='fileaccesslog',
            name='appointment',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='appointments.appointment'),
        ),
        migrations.AddField(
            model_name='fileaccesslog',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
    ]
