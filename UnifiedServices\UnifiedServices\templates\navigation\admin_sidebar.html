{% comment %}
Admin Sidebar Navigation Component
This component provides the navigation menu specifically for admin users.
{% endcomment %}

{% block admin_mobile_nav %}
<ul role="list" class="flex flex-1 flex-col gap-y-7">
    <li>
        <div class="text-xs font-semibold leading-6 text-emerald-600 mb-2">ADMIN PORTAL</div>
        <ul role="list" class="-mx-2 space-y-1">
            <li>
                <a href="{% url 'admin_dashboard' %}"
                   class="{% if request.resolver_match.url_name == 'admin_dashboard' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'admin_dashboard' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                        </svg>
                    </div>
                    Dashboard
                </a>
            </li>
            <li>
                <a href="{% url 'admin_appointment_list' %}"
                   class="{% if request.resolver_match.url_name == 'admin_appointment_list' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'admin_appointment_list' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 17.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                        </svg>
                    </div>
                    All Appointments
                </a>
            </li>
            <li>
                <a href="{% url 'user_management' %}"
                   class="{% if request.resolver_match.url_name == 'user_management' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'user_management' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
                        </svg>
                    </div>
                    User Management
                </a>
            </li>
            <li>
                <a href="{% url 'department_list' %}"
                   class="{% if request.resolver_match.url_name == 'department_list' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'department_list' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                    Departments
                </a>
            </li>
            <li>
                <a href="{% url 'admin_reports' %}"
                   class="{% if request.resolver_match.url_name == 'admin_reports' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'admin_reports' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
                        </svg>
                    </div>
                    Reports
                </a>
            </li>
            <li>
                <a href="{% url 'system_settings' %}"
                   class="{% if request.resolver_match.url_name == 'system_settings' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'system_settings' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </div>
                    System Settings
                </a>
            </li>
        </ul>
    </li>
</ul>
{% endblock %}

{% block admin_desktop_nav %}
<ul role="list" class="flex flex-1 flex-col gap-y-7">
    <li>
        <div class="text-xs font-semibold leading-6 text-emerald-600 mb-2">ADMIN PORTAL</div>
        <ul role="list" class="-mx-2 space-y-1">
            <li>
                <a href="{% url 'admin_dashboard' %}"
                   class="{% if request.resolver_match.url_name == 'admin_dashboard' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'admin_dashboard' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                        </svg>
                    </div>
                    Dashboard
                </a>
            </li>
            <li>
                <a href="{% url 'admin_appointment_list' %}"
                   class="{% if request.resolver_match.url_name == 'admin_appointment_list' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'admin_appointment_list' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 17.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                        </svg>
                    </div>
                    All Appointments
                </a>
            </li>
            <li>
                <a href="{% url 'user_management' %}"
                   class="{% if request.resolver_match.url_name == 'user_management' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'user_management' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
                        </svg>
                    </div>
                    User Management
                </a>
            </li>
            <li>
                <a href="{% url 'department_list' %}"
                   class="{% if request.resolver_match.url_name == 'department_list' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'department_list' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                    Departments
                </a>
            </li>
            <li>
                <a href="{% url 'admin_reports' %}"
                   class="{% if request.resolver_match.url_name == 'admin_reports' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'admin_reports' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
                        </svg>
                    </div>
                    Reports
                </a>
            </li>
            <li>
                <a href="{% url 'system_settings' %}"
                   class="{% if request.resolver_match.url_name == 'system_settings' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'system_settings' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </div>
                    System Settings
                </a>
            </li>
        </ul>
    </li>
</ul>
{% endblock %}