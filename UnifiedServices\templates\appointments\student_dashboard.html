{% extends 'base.html' %}

{% block title %}Student Dashboard - JHCSC Unified Student Services{% endblock %}

{% block page_title %}Student Dashboard{% endblock %}

{% block extra_head %}
<!-- Custom Tailwind Config for Emerald & Gold Theme -->
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    emerald: {
                        50: '#ecfdf5',
                        100: '#d1fae5',
                        200: '#a7f3d0',
                        300: '#6ee7b7',
                        400: '#34d399',
                        500: '#10b981',
                        600: '#059669',
                        700: '#047857',
                        800: '#065f46',
                        900: '#064e3b',
                        950: '#022c22'
                    },
                    gold: {
                        50: '#fffbeb',
                        100: '#fef3c7',
                        200: '#fde68a',
                        300: '#fcd34d',
                        400: '#fbbf24',
                        500: '#f59e0b',
                        600: '#d97706',
                        700: '#b45309',
                        800: '#92400e',
                        900: '#78350f'
                    }
                },
                animation: {
                    'fade-in': 'fadeIn 0.6s ease-out',
                    'slide-up': 'slideUp 0.6s ease-out',
                    'card-hover': 'cardHover 0.3s ease',
                },
                keyframes: {
                    fadeIn: {
                        '0%': { opacity: '0' },
                        '100%': { opacity: '1' }
                    },
                    slideUp: {
                        '0%': { opacity: '0', transform: 'translateY(20px)' },
                        '100%': { opacity: '1', transform: 'translateY(0)' }
                    },
                    cardHover: {
                        '0%': { transform: 'translateY(0)' },
                        '100%': { transform: 'translateY(-4px)' }
                    }
                }
            }
        }
    }
</script>

<style>
    [x-cloak] { display: none !important; }

    .card-hover {
        transition: all 0.3s ease;
    }

    .card-hover:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .text-gradient {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .hero-gradient {
        background: linear-gradient(135deg, #064e3b 0%, #047857 50%, #059669 100%);
    }
</style>
{% endblock %}

{% block mobile_nav %}
{% include 'navigation/student_nav.html' %}
{% endblock %}

{% block desktop_nav %}
{% include 'navigation/student_nav.html' %}
{% endblock %}

{% block content %}
<div class="animate-fade-in">
    <!-- Welcome Section -->
    <div class="bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-2xl p-6 mb-8 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">Welcome back, {{ user.first_name|default:user.username }}!</h1>
                <p class="text-emerald-100">Here's an overview of your academic services and appointments.</p>
            </div>
            <div class="hidden md:block">
                <div class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-gold-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.26 10.147a60.436 60.436 0 00-.491 6.347A48.627 48.627 0 0112 20.904a48.627 48.627 0 018.232-4.41 60.46 60.46 0 00-.491-6.347m-15.482 0a50.57 50.57 0 00-2.658-.813A59.905 59.905 0 0112 3.493a59.902 59.902 0 0110.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.697 50.697 0 0112 13.489a50.702 50.702 0 017.74-3.342M6.75 15a.75.75 0 100-1.5.75.75 0 000 1.5zm0 0v-3.675A55.378 55.378 0 0112 8.443a55.381 55.381 0 015.25 2.882V15" />
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats overview -->
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8 animate-slide-up">
        <div class="bg-white overflow-hidden shadow-lg rounded-xl border border-gray-100 card-hover">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-gold-400 to-gold-500 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Pending Appointments</dt>
                            <dd class="text-2xl font-bold text-gray-900">{{ pending_count }}</dd>
                            <dd class="text-xs text-gold-600 font-medium">Awaiting processing</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-xl border border-gray-100 card-hover">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-emerald-400 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Ready for Pickup</dt>
                            <dd class="text-2xl font-bold text-gray-900">{{ ready_count }}</dd>
                            <dd class="text-xs text-emerald-600 font-medium">Available now</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-xl border border-gray-100 card-hover">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-500 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Appointments</dt>
                            <dd class="text-2xl font-bold text-gray-900">{{ appointments.count }}</dd>
                            <dd class="text-xs text-blue-600 font-medium">All time</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-xl border border-gray-100 card-hover">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-500 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Available Departments</dt>
                            <dd class="text-2xl font-bold text-gray-900">{{ departments.count }}</dd>
                            <dd class="text-xs text-purple-600 font-medium">Services offered</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick actions -->
    <div class="bg-white shadow-xl rounded-2xl mb-8 border border-gray-100">
        <div class="px-6 py-8">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-gray-900">Quick Actions</h3>
                <div class="w-8 h-8 bg-gradient-to-r from-emerald-500 to-gold-500 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                </div>
            </div>
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                <a href="{% url 'appointment_create' %}" class="group relative block w-full rounded-xl border-2 border-dashed border-emerald-200 p-6 text-center hover:border-emerald-400 hover:bg-emerald-50 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 transition-all duration-200 card-hover">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                        </svg>
                    </div>
                    <span class="block text-lg font-semibold text-gray-900 mb-2">New Appointment</span>
                    <span class="block text-sm text-gray-500">Schedule a new service request</span>
                </a>

                <a href="{% url 'appointment_list' %}" class="group relative block w-full rounded-xl border-2 border-dashed border-blue-200 p-6 text-center hover:border-blue-400 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 card-hover">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </div>
                    <span class="block text-lg font-semibold text-gray-900 mb-2">My Appointments</span>
                    <span class="block text-sm text-gray-500">View and track your requests</span>
                </a>

                <a href="{% url 'service_list' %}" class="group relative block w-full rounded-xl border-2 border-dashed border-gold-200 p-6 text-center hover:border-gold-400 hover:bg-gold-50 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2 transition-all duration-200 card-hover">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-gold-500 to-gold-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                    <span class="block text-lg font-semibold text-gray-900 mb-2">Browse Services</span>
                    <span class="block text-sm text-gray-500">Explore available services</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Recent appointments -->
    <div class="bg-white shadow-xl rounded-2xl border border-gray-100">
        <div class="px-6 py-8">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-gray-900">Recent Appointments</h3>
                <a href="{% url 'appointment_list' %}" class="text-emerald-600 hover:text-emerald-700 font-medium text-sm transition-colors duration-200">
                    View all →
                </a>
            </div>
            {% if appointments %}
            <div class="space-y-4">
                {% for appointment in appointments|slice:":5" %}
                <div class="bg-gray-50 rounded-xl p-4 hover:bg-gray-100 transition-colors duration-200 card-hover">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <div class="h-12 w-12 rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center shadow-lg">
                                <span class="text-sm font-bold text-white">{{ appointment.service.department.code }}</span>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-lg font-semibold text-gray-900 truncate">{{ appointment.service.name }}</p>
                            <p class="text-sm text-gray-500 mb-2">{{ appointment.service.department.name }}</p>
                            <p class="text-xs text-gray-400">{{ appointment.created_at|date:"M d, Y" }}</p>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold
                                {% if appointment.status == 'pending' %}bg-gradient-to-r from-gold-100 to-gold-200 text-gold-800
                                {% elif appointment.status == 'in_progress' %}bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800
                                {% elif appointment.status == 'ready' %}bg-gradient-to-r from-emerald-100 to-emerald-200 text-emerald-800
                                {% elif appointment.status == 'claimed' %}bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800
                                {% else %}bg-gradient-to-r from-red-100 to-red-200 text-red-800{% endif %}">
                                {{ appointment.get_status_display }}
                            </span>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-12">
                <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center">
                    <svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">No appointments yet</h3>
                <p class="text-gray-500 mb-6">Get started by creating your first appointment.</p>
                <a href="{% url 'appointment_create' %}" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-600 to-emerald-700 text-white font-semibold rounded-xl hover:from-emerald-700 hover:to-emerald-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-all duration-200 shadow-lg hover:shadow-xl">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>
                    Create New Appointment
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
