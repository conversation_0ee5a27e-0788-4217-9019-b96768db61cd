"""
Security utilities and middleware for the Unified Services system
"""

import json
import re
import hashlib
import hmac
from datetime import datetime, timedelta
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.http import HttpResponseForbidden
from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin
from .models import AuditLog, SecurityEvent, FileAccessLog

User = get_user_model()


class SecurityMiddleware(MiddlewareMixin):
    """Middleware for security monitoring and threat detection"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """Process incoming requests for security threats"""
        # Check for suspicious patterns
        self.check_sql_injection(request)
        self.check_xss_attempts(request)
        self.check_brute_force(request)
        
        return None
    
    def process_response(self, request, response):
        """Process responses and log security events"""
        # Log failed authentication attempts
        if hasattr(request, 'user') and request.user.is_authenticated:
            if response.status_code == 403:
                self.log_unauthorized_access(request, response)
        
        return response
    
    def check_sql_injection(self, request):
        """Check for SQL injection attempts"""
        sql_patterns = [
            r"(\bunion\b.*\bselect\b)",
            r"(\bselect\b.*\bfrom\b)",
            r"(\binsert\b.*\binto\b)",
            r"(\bdelete\b.*\bfrom\b)",
            r"(\bdrop\b.*\btable\b)",
            r"(\bor\b.*1\s*=\s*1)",
            r"(\band\b.*1\s*=\s*1)",
            r"(\bexec\b.*\bxp_)",
        ]
        
        for param_name, param_value in request.GET.items():
            if self._check_patterns(param_value, sql_patterns):
                self.log_security_event(
                    request, 'sql_injection', 'high',
                    f'SQL injection attempt in GET parameter: {param_name}'
                )
        
        if request.method == 'POST':
            for param_name, param_value in request.POST.items():
                if isinstance(param_value, str) and self._check_patterns(param_value, sql_patterns):
                    self.log_security_event(
                        request, 'sql_injection', 'high',
                        f'SQL injection attempt in POST parameter: {param_name}'
                    )
    
    def check_xss_attempts(self, request):
        """Check for XSS attempts"""
        xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"on\w+\s*=",
            r"<iframe[^>]*>",
            r"<object[^>]*>",
            r"<embed[^>]*>",
        ]
        
        for param_name, param_value in request.GET.items():
            if self._check_patterns(param_value, xss_patterns):
                self.log_security_event(
                    request, 'xss_attempt', 'medium',
                    f'XSS attempt in GET parameter: {param_name}'
                )
        
        if request.method == 'POST':
            for param_name, param_value in request.POST.items():
                if isinstance(param_value, str) and self._check_patterns(param_value, xss_patterns):
                    self.log_security_event(
                        request, 'xss_attempt', 'medium',
                        f'XSS attempt in POST parameter: {param_name}'
                    )
    
    def check_brute_force(self, request):
        """Check for brute force attacks"""
        if request.path == '/login/' and request.method == 'POST':
            ip_address = AuditLog.get_client_ip(request)
            cache_key = f"failed_login_{ip_address}"
            
            failed_attempts = cache.get(cache_key, 0)
            
            if failed_attempts >= 5:
                self.log_security_event(
                    request, 'brute_force', 'high',
                    f'Brute force attack detected from IP: {ip_address}'
                )
                return HttpResponseForbidden("Too many failed login attempts")
    
    def log_unauthorized_access(self, request, response):
        """Log unauthorized access attempts"""
        AuditLog.log_action(
            user=request.user if request.user.is_authenticated else None,
            action='permission_denied',
            description=f'Unauthorized access attempt to {request.path}',
            request=request,
            additional_data={'status_code': response.status_code}
        )
    
    def log_security_event(self, request, event_type, severity, description):
        """Log security events"""
        SecurityEvent.objects.create(
            event_type=event_type,
            severity=severity,
            user=request.user if request.user.is_authenticated else None,
            ip_address=AuditLog.get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            description=description,
            request_path=request.path,
            request_data=json.dumps({
                'GET': dict(request.GET),
                'POST': dict(request.POST)
            })
        )
    
    def _check_patterns(self, text, patterns):
        """Check if text matches any of the given patterns"""
        if not isinstance(text, str):
            return False
        
        text_lower = text.lower()
        for pattern in patterns:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return True
        return False


class FileSecurityManager:
    """Manage file security and access control"""
    
    ALLOWED_EXTENSIONS = {
        'pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'gif'
    }
    
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    
    @classmethod
    def validate_file(cls, uploaded_file):
        """Validate uploaded file for security"""
        errors = []
        
        # Check file size
        if uploaded_file.size > cls.MAX_FILE_SIZE:
            errors.append(f"File size exceeds maximum allowed size of {cls.MAX_FILE_SIZE // (1024*1024)}MB")
        
        # Check file extension
        file_extension = uploaded_file.name.split('.')[-1].lower()
        if file_extension not in cls.ALLOWED_EXTENSIONS:
            errors.append(f"File type '{file_extension}' is not allowed")
        
        # Check for malicious content
        if cls._check_malicious_content(uploaded_file):
            errors.append("File contains potentially malicious content")
        
        return errors
    
    @classmethod
    def _check_malicious_content(cls, uploaded_file):
        """Basic check for malicious content in files"""
        try:
            # Read first 1KB of file to check for suspicious patterns
            uploaded_file.seek(0)
            content = uploaded_file.read(1024)
            uploaded_file.seek(0)
            
            # Convert bytes to string for pattern matching
            if isinstance(content, bytes):
                try:
                    content = content.decode('utf-8', errors='ignore')
                except:
                    return False
            
            malicious_patterns = [
                r'<script[^>]*>',
                r'javascript:',
                r'vbscript:',
                r'onload\s*=',
                r'onerror\s*=',
                r'eval\s*\(',
                r'document\.cookie',
            ]
            
            for pattern in malicious_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    return True
            
            return False
        except:
            # If we can't read the file, consider it suspicious
            return True
    
    @classmethod
    def log_file_access(cls, user, appointment, file_path, file_name, access_type, 
                       request, success=True, error_message='', file_size=None):
        """Log file access for auditing"""
        FileAccessLog.objects.create(
            user=user,
            appointment=appointment,
            file_path=file_path,
            file_name=file_name,
            access_type=access_type,
            ip_address=AuditLog.get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            success=success,
            error_message=error_message,
            file_size=file_size
        )


class QRSecurityManager:
    """Manage QR code security and validation"""
    
    @classmethod
    def validate_qr_integrity(cls, qr_data, expected_hash):
        """Validate QR code integrity using HMAC"""
        try:
            # Extract data for hash verification
            hash_data = f"{qr_data['appointment_id']}:{qr_data['student_id']}:{qr_data['service']}:{qr_data['created_at']}"
            
            # Generate expected hash
            calculated_hash = hmac.new(
                settings.SECRET_KEY.encode(),
                hash_data.encode(),
                hashlib.sha256
            ).hexdigest()
            
            return calculated_hash == expected_hash
        except (KeyError, TypeError):
            return False
    
    @classmethod
    def log_qr_scan(cls, user, appointment, request, success=True, error_message=''):
        """Log QR code scanning attempts"""
        action = 'qr_verified' if success else 'qr_tampering'
        description = f"QR code scan {'successful' if success else 'failed'} for appointment {appointment.appointment_id if appointment else 'unknown'}"
        
        if not success and error_message:
            description += f": {error_message}"
        
        AuditLog.log_action(
            user=user,
            action=action,
            description=description,
            appointment=appointment,
            request=request
        )
        
        # Log security event for failed QR scans
        if not success:
            SecurityEvent.objects.create(
                event_type='qr_tampering',
                severity='medium',
                user=user,
                ip_address=AuditLog.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                description=f"QR code tampering detected: {error_message}",
                request_path=request.path
            )


def rate_limit_check(request, action, limit=5, window=300):
    """Check if user has exceeded rate limit for specific action"""
    if request.user.is_authenticated:
        cache_key = f"rate_limit_{request.user.id}_{action}"
    else:
        ip_address = AuditLog.get_client_ip(request)
        cache_key = f"rate_limit_ip_{ip_address}_{action}"
    
    current_count = cache.get(cache_key, 0)
    
    if current_count >= limit:
        return False, f"Rate limit exceeded for {action}"
    
    # Increment counter
    cache.set(cache_key, current_count + 1, window)
    return True, "OK"


def generate_secure_token(length=32):
    """Generate a secure random token"""
    import secrets
    return secrets.token_urlsafe(length)


def hash_sensitive_data(data):
    """Hash sensitive data for storage"""
    return hashlib.sha256(data.encode()).hexdigest()
