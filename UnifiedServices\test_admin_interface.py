#!/usr/bin/env python
"""
Test script for Administrator Interface functionality
"""

import os
import sys
import django
from django.test import Client
from django.contrib.auth import get_user_model
from django.urls import reverse

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UnifiedServices.settings')
django.setup()

from appointments.models import *

def test_admin_interface():
    """Test administrator interface functionality"""
    print("🧪 Administrator Interface Test")
    print("=" * 50)
    
    client = Client()
    User = get_user_model()
    
    try:
        # Get admin user
        admin_user = User.objects.get(username='admin1')
        print(f"✅ Admin user: {admin_user.get_full_name()}")
        print(f"   Role: {admin_user.userprofile.role}")
        
        # Login as admin
        login_success = client.login(username='admin1', password='password123')
        if login_success:
            print("✅ Admin login successful")
        else:
            print("❌ Admin login failed")
            return False
        
        # Test admin dashboard
        response = client.get(reverse('admin_dashboard'))
        print(f"✅ Admin dashboard: {response.status_code}")
        if response.status_code != 200:
            print(f"❌ Dashboard failed: {response.status_code}")
            return False
        
        # Test admin appointment list
        response = client.get(reverse('admin_appointment_list'))
        print(f"✅ Admin appointment list: {response.status_code}")
        
        # Test admin user list
        response = client.get(reverse('admin_user_list'))
        print(f"✅ Admin user list: {response.status_code}")
        
        # Test admin department list
        response = client.get(reverse('admin_department_list'))
        print(f"✅ Admin department list: {response.status_code}")
        
        # Test admin reports
        response = client.get(reverse('admin_reports'))
        print(f"✅ Admin reports: {response.status_code}")
        
        # Test filtering on appointment list
        response = client.get(reverse('admin_appointment_list') + '?status=pending')
        print(f"✅ Admin appointment filtering: {response.status_code}")
        
        # Test filtering on user list
        response = client.get(reverse('admin_user_list') + '?role=student')
        print(f"✅ Admin user filtering: {response.status_code}")
        
        # Test search functionality
        response = client.get(reverse('admin_appointment_list') + '?search=APP')
        print(f"✅ Admin appointment search: {response.status_code}")
        
        # Test access control - try with non-admin user
        client.logout()
        
        # Login as student
        student_user = User.objects.filter(userprofile__role='student').first()
        if student_user:
            client.login(username=student_user.username, password='password123')
            
            # Try to access admin dashboard (should be denied)
            response = client.get(reverse('admin_dashboard'))
            if response.status_code == 403:
                print("✅ Access control working - student denied admin access")
            else:
                print(f"❌ Access control failed - student got {response.status_code}")
                return False
        
        print("\n🎉 All Administrator Interface Tests Passed!")
        print("=" * 50)
        print("✅ Admin Dashboard Access")
        print("✅ Admin Appointment Management")
        print("✅ Admin User Management")
        print("✅ Admin Department Overview")
        print("✅ Admin System Reports")
        print("✅ Filtering and Search")
        print("✅ Access Control")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_admin_interface()
    sys.exit(0 if success else 1)
