{% extends 'base.html' %}

{% block title %}Create Appointment - JHCSC Unified Student Services{% endblock %}

{% block page_title %}Create New Appointment{% endblock %}

{% block mobile_nav %}
<ul role="list" class="flex flex-1 flex-col gap-y-7">
    <li>
        <ul role="list" class="-mx-2 space-y-1">
            <li>
                <a href="{% url 'student_dashboard' %}" 
                   class="text-gray-700 hover:text-indigo-600 hover:bg-gray-50 group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                    <svg class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                    </svg>
                    Dashboard
                </a>
            </li>
            <li>
                <a href="{% url 'appointment_create' %}" 
                   class="bg-gray-50 text-indigo-700 group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                    <svg class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>
                    New Appointment
                </a>
            </li>
            <li>
                <a href="{% url 'appointment_list' %}" 
                   class="text-gray-700 hover:text-indigo-600 hover:bg-gray-50 group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                    <svg class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 17.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                    </svg>
                    My Appointments
                </a>
            </li>
        </ul>
    </li>
</ul>
{% endblock %}

{% block desktop_nav %}
<ul role="list" class="flex flex-1 flex-col gap-y-7">
    <li>
        <ul role="list" class="-mx-2 space-y-1">
            <li>
                <a href="{% url 'student_dashboard' %}" 
                   class="text-gray-700 hover:text-indigo-600 hover:bg-gray-50 group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                    <svg class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                    </svg>
                    Dashboard
                </a>
            </li>
            <li>
                <a href="{% url 'appointment_create' %}" 
                   class="bg-gray-50 text-indigo-700 group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                    <svg class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>
                    New Appointment
                </a>
            </li>
            <li>
                <a href="{% url 'appointment_list' %}" 
                   class="text-gray-700 hover:text-indigo-600 hover:bg-gray-50 group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                    <svg class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 17.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                    </svg>
                    My Appointments
                </a>
            </li>
        </ul>
    </li>
</ul>
{% endblock %}

{% block content %}
<div class="max-w-3xl mx-auto">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol role="list" class="flex items-center space-x-4">
            <li>
                <div>
                    <a href="{% url 'student_dashboard' %}" class="text-gray-400 hover:text-gray-500">
                        <svg class="flex-shrink-0 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                        </svg>
                        <span class="sr-only">Home</span>
                    </a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                    </svg>
                    <span class="ml-4 text-sm font-medium text-gray-500">Create Appointment</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-6">Request New Service</h3>
            
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Service Selection -->
                <div>
                    <label for="{{ form.service.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Service *
                    </label>
                    <div class="mt-1">
                        <select id="{{ form.service.id_for_label }}" 
                                name="{{ form.service.name }}" 
                                required
                                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                x-data="{ selectedService: '' }"
                                x-model="selectedService"
                                @change="updateServiceInfo()">
                            <option value="">Select a service...</option>
                            {% for service in form.service.field.queryset %}
                            <option value="{{ service.id }}" 
                                    data-department="{{ service.department.name }}"
                                    data-description="{{ service.description }}"
                                    data-processing-time="{{ service.processing_time }}">
                                {{ service.department.name }} - {{ service.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    {% if form.service.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.service.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Service Information (shown when service is selected) -->
                <div id="service-info" class="hidden bg-blue-50 border border-blue-200 rounded-md p-4">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">Service Information</h4>
                    <div class="text-sm text-blue-800">
                        <p><strong>Department:</strong> <span id="service-department"></span></p>
                        <p><strong>Description:</strong> <span id="service-description"></span></p>
                        <p><strong>Processing Time:</strong> <span id="service-processing-time"></span></p>
                    </div>
                </div>

                <!-- Notes -->
                <div>
                    <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Additional Notes
                    </label>
                    <div class="mt-1">
                        <textarea id="{{ form.notes.id_for_label }}" 
                                  name="{{ form.notes.name }}" 
                                  rows="3" 
                                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                  placeholder="Any additional information or special requests...">{{ form.notes.value|default:'' }}</textarea>
                    </div>
                    {% if form.notes.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.notes.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3">
                    <a href="{% url 'student_dashboard' %}" 
                       class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Create Appointment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function updateServiceInfo() {
    const select = document.querySelector('select[name="service"]');
    const serviceInfo = document.getElementById('service-info');
    const selectedOption = select.options[select.selectedIndex];
    
    if (selectedOption.value) {
        document.getElementById('service-department').textContent = selectedOption.dataset.department;
        document.getElementById('service-description').textContent = selectedOption.dataset.description;
        document.getElementById('service-processing-time').textContent = selectedOption.dataset.processingTime;
        serviceInfo.classList.remove('hidden');
    } else {
        serviceInfo.classList.add('hidden');
    }
}
</script>
{% endblock %}
