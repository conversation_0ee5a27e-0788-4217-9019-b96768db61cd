# Design Document

## Overview

The Unified Student Services Appointment System is a Django-based web application that provides a mobile-first, role-based platform for managing student service requests across multiple departments at JHCSC. The system leverages modern web technologies including HTMX for dynamic interactions, Tailwind CSS for responsive design, and Alpine.js for client-side interactivity.

The architecture follows Django's MVT (Model-View-Template) pattern with additional layers for role-based access control, QR code generation/scanning, and real-time updates. The system is designed to be fully responsive and optimized for mobile devices while maintaining full functionality on desktop platforms.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Client Browser] --> B[Django Web Server]
    B --> C[Authentication Layer]
    C --> D[Role-Based Access Control]
    D --> E[Business Logic Layer]
    E --> F[Data Access Layer]
    F --> G[SQLite Database]
    
    E --> H[QR Code Service]
    E --> I[File Upload Service]
    E --> J[Notification Service]
    
    A --> K[Static Assets]
    K --> L[Tailwind CSS]
    K --> M[HTMX]
    K --> N[Alpine.js]
    K --> O[Unpoly.js]
```

### Technology Stack Integration

- **Backend Framework**: Django 5.2.4 with built-in authentication
- **Database**: SQLite (existing setup, suitable for JHCSC scale)
- **Frontend Styling**: Tailwind CSS via CDN for rapid responsive development
- **Dynamic Interactions**: HTMX for server-side rendering with AJAX-like behavior
- **Client-Side Logic**: Alpine.js for reactive components and sidebar management
- **Page Transitions**: Unpoly.js for smooth navigation
- **QR Code Generation**: `qrcode` Python library with PIL for image generation
- **File Handling**: Django's built-in file upload with secure storage in media directory

### Security Architecture

- **Authentication**: Django's built-in User model extended with role fields
- **Authorization**: Custom decorators and mixins for role-based access control
- **QR Code Security**: HMAC-based verification hash to prevent tampering
- **File Security**: Secure file upload validation and storage
- **CSRF Protection**: Django's built-in CSRF middleware
- **Session Management**: Django's secure session handling

## Components and Interfaces

### Core Models

#### Extended User Model
```python
class UserProfile(models.Model):
    ROLE_CHOICES = [
        ('student', 'Student'),
        ('office_staff', 'Office Staff'),
        ('admin', 'Administrator'),
    ]
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    department = models.ForeignKey('Department', null=True, blank=True)
    student_id = models.CharField(max_length=20, null=True, blank=True)
    phone_number = models.CharField(max_length=15, null=True, blank=True)
```

#### Department and Service Models
```python
class Department(models.Model):
    name = models.CharField(max_length=100)  # Registrar, Accounting, Guidance
    code = models.CharField(max_length=10, unique=True)
    description = models.TextField()
    is_active = models.BooleanField(default=True)

class Service(models.Model):
    department = models.ForeignKey(Department, on_delete=models.CASCADE)
    name = models.CharField(max_length=200)  # Certificate of Good Moral
    description = models.TextField()
    processing_time = models.CharField(max_length=50)
    is_active = models.BooleanField(default=True)

class ServiceRequirement(models.Model):
    service = models.ForeignKey(Service, on_delete=models.CASCADE)
    name = models.CharField(max_length=200)
    description = models.TextField()
    is_required = models.BooleanField(default=True)
    requires_upload = models.BooleanField(default=False)
```

#### Appointment System Models
```python
class Appointment(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('ready', 'Ready for Pickup'),
        ('claimed', 'Claimed'),
        ('cancelled', 'Cancelled'),
    ]
    
    appointment_id = models.CharField(max_length=20, unique=True)
    student = models.ForeignKey(User, on_delete=models.CASCADE)
    service = models.ForeignKey(Service, on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    qr_code = models.ImageField(upload_to='qr_codes/', null=True, blank=True)
    qr_hash = models.CharField(max_length=64, null=True, blank=True)

class AppointmentRequirement(models.Model):
    appointment = models.ForeignKey(Appointment, on_delete=models.CASCADE)
    requirement = models.ForeignKey(ServiceRequirement, on_delete=models.CASCADE)
    is_completed = models.BooleanField(default=False)
    completed_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL)
    completed_at = models.DateTimeField(null=True, blank=True)
    uploaded_file = models.FileField(upload_to='documents/', null=True, blank=True)
    notes = models.TextField(blank=True)
```

### View Layer Architecture

#### Role-Based View Mixins
```python
class RoleRequiredMixin:
    required_role = None
    
    def dispatch(self, request, *args, **kwargs):
        if not self.has_required_role(request.user):
            return redirect('unauthorized')
        return super().dispatch(request, *args, **kwargs)

class StudentRequiredMixin(RoleRequiredMixin):
    required_role = 'student'

class OfficeStaffRequiredMixin(RoleRequiredMixin):
    required_role = 'office_staff'

class AdminRequiredMixin(RoleRequiredMixin):
    required_role = 'admin'
```

#### Dashboard Views
- **StudentDashboardView**: Displays student's appointments, available services, and QR codes
- **OfficeStaffDashboardView**: Shows department-specific appointments and scanning interface
- **AdminDashboardView**: Comprehensive system overview with reports and user management

#### HTMX-Enabled Views
- **RequirementUpdateView**: Real-time requirement status updates
- **QRScannerView**: Mobile QR code scanning interface
- **AppointmentStatusView**: Live appointment status updates
- **FileUploadView**: Asynchronous file upload with progress

### QR Code Service

#### QR Code Generation
```python
class QRCodeService:
    @staticmethod
    def generate_qr_code(appointment):
        # Create verification hash
        hash_data = f"{appointment.appointment_id}:{appointment.student.id}:{appointment.service.id}"
        verification_hash = hmac.new(
            settings.SECRET_KEY.encode(),
            hash_data.encode(),
            hashlib.sha256
        ).hexdigest()
        
        # Generate QR code data
        qr_data = {
            'appointment_id': appointment.appointment_id,
            'student_name': appointment.student.get_full_name(),
            'service': appointment.service.name,
            'department': appointment.service.department.name,
            'hash': verification_hash
        }
        
        # Create QR code image
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(json.dumps(qr_data))
        qr.make(fit=True)
        
        return qr.make_image(fill_color="black", back_color="white")
```

### Frontend Component Architecture

#### Alpine.js Components
- **Sidebar Component**: Collapsible navigation for mobile/desktop
- **File Upload Component**: Drag-and-drop file upload with progress
- **QR Scanner Component**: Camera access and QR code detection
- **Notification Component**: Toast notifications for user feedback

#### HTMX Integration Patterns
- **hx-get**: Load appointment details and status updates
- **hx-post**: Submit forms and update requirements
- **hx-trigger**: Real-time updates on status changes
- **hx-target**: Update specific page sections without full reload
- **hx-swap**: Control how content is replaced in the DOM

## Data Models

### Database Schema

```mermaid
erDiagram
    User ||--|| UserProfile : has
    UserProfile }o--|| Department : belongs_to
    Department ||--o{ Service : offers
    Service ||--o{ ServiceRequirement : requires
    User ||--o{ Appointment : creates
    Appointment }o--|| Service : for
    Appointment ||--o{ AppointmentRequirement : has
    AppointmentRequirement }o--|| ServiceRequirement : fulfills
    Appointment ||--o{ AuditLog : generates
    User ||--o{ AuditLog : performs
```

### Data Relationships

1. **User-Profile Relationship**: One-to-one relationship extending Django's User model
2. **Department-Service Hierarchy**: Departments contain multiple services
3. **Service-Requirement Mapping**: Each service has specific requirements
4. **Appointment-Requirement Tracking**: Individual requirement completion tracking
5. **Audit Trail**: Complete logging of all system interactions

### File Storage Strategy

- **Document Uploads**: Stored in `media/documents/` with UUID-based filenames
- **QR Code Images**: Generated and stored in `media/qr_codes/`
- **File Validation**: MIME type checking and size limits
- **Access Control**: Secure file serving based on user permissions

## Error Handling

### Exception Handling Strategy

#### Custom Exception Classes
```python
class AppointmentSystemException(Exception):
    pass

class QRCodeValidationError(AppointmentSystemException):
    pass

class RequirementNotMetError(AppointmentSystemException):
    pass

class UnauthorizedAccessError(AppointmentSystemException):
    pass
```

#### Error Response Patterns
- **HTMX Errors**: Return partial HTML with error messages
- **API Errors**: JSON responses with error codes and messages
- **File Upload Errors**: Real-time validation feedback
- **QR Code Errors**: Clear user messaging for scan failures

### Validation Framework

#### Form Validation
- **Client-Side**: Alpine.js for immediate feedback
- **Server-Side**: Django forms with custom validators
- **File Validation**: Type, size, and content validation
- **QR Code Validation**: Hash verification and expiration checks

#### Business Logic Validation
- **Role Permissions**: Middleware-level access control
- **Appointment Status**: State machine validation
- **Requirement Completion**: Dependency checking
- **QR Code Generation**: Prerequisite validation

## Testing Strategy

### Unit Testing
- **Model Tests**: Data validation and business logic
- **View Tests**: HTTP responses and template rendering
- **Service Tests**: QR code generation and validation
- **Form Tests**: Input validation and error handling

### Integration Testing
- **Authentication Flow**: Login/logout and role-based access
- **Appointment Workflow**: End-to-end appointment creation and completion
- **QR Code Workflow**: Generation, download, and scanning
- **File Upload Flow**: Upload, validation, and storage

### Frontend Testing
- **HTMX Interactions**: Dynamic content loading and updates
- **Alpine.js Components**: Reactive behavior and state management
- **Responsive Design**: Cross-device compatibility
- **Accessibility**: Screen reader and keyboard navigation

### Performance Testing
- **Database Queries**: Optimization and N+1 prevention
- **File Upload**: Large file handling and progress tracking
- **QR Code Generation**: Batch processing capabilities
- **Mobile Performance**: Touch interactions and loading times

### Security Testing
- **Authentication**: Login security and session management
- **Authorization**: Role-based access enforcement
- **File Security**: Upload validation and access control
- **QR Code Security**: Hash validation and replay prevention
- **CSRF Protection**: Form submission security
- **SQL Injection**: Parameterized query validation