from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.models import User
from django.contrib import messages
from django.views.generic import TemplateView, ListView, DetailView, CreateView, UpdateView
from django.http import JsonResponse, HttpResponse, Http404
from django.urls import reverse_lazy
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.core.exceptions import PermissionDenied
from django.db import transaction
from django.utils import timezone
import json
import qrcode
import base64
from io import BytesIO
from io import BytesIO
import base64
from PIL import Image

from .models import (
    UserProfile, Department, Service, ServiceRequirement,
    Appointment, AppointmentRequirement, AuditLog, SecurityEvent, FileAccessLog
)
from .security import FileSecurityManager, QRSecurityManager, rate_limit_check
from .audit import AuditLogger
from datetime import datetime, timedelta
from django.db.models import Count, Q
import csv


class LandingPageView(TemplateView):
    """Modern interactive landing page for the Unified Student Services system"""
    template_name = 'landing.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get system statistics for the landing page
        context.update({
            'total_departments': Department.objects.count(),
            'total_services': Service.objects.count(),
            'total_appointments': Appointment.objects.count(),
            'active_students': UserProfile.objects.filter(role='student').count(),
        })

        # Get featured departments and services
        context['featured_departments'] = Department.objects.all()[:6]
        # Get services without annotation for now - will add appointment count later
        context['popular_services'] = Service.objects.all()[:8]

        return context


class RoleRequiredMixin:
    """Base mixin for role-based access control"""
    required_role = None

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('login')

        if not self.has_required_role(request.user):
            messages.error(request, "You don't have permission to access this page.")
            return redirect('unauthorized')

        return super().dispatch(request, *args, **kwargs)

    def has_required_role(self, user):
        if not hasattr(user, 'userprofile'):
            return False

        if self.required_role is None:
            return True

        if isinstance(self.required_role, list):
            return user.userprofile.role in self.required_role

        return user.userprofile.role == self.required_role


class StudentRequiredMixin(RoleRequiredMixin):
    required_role = 'student'


class OfficeStaffRequiredMixin(RoleRequiredMixin):
    required_role = 'office_staff'


class AdminRequiredMixin(RoleRequiredMixin):
    required_role = 'admin'


class StaffOrAdminRequiredMixin(RoleRequiredMixin):
    required_role = ['office_staff', 'admin']


class UnauthorizedView(TemplateView):
    template_name = 'registration/unauthorized.html'


def dashboard_redirect(request):
    """Redirect users to appropriate dashboard based on role"""
    if not request.user.is_authenticated:
        return redirect('login')

    if not hasattr(request.user, 'userprofile'):
        messages.error(request, "Your account is not properly configured. Please contact an administrator.")
        return redirect('login')

    role = request.user.userprofile.role

    if role == 'student':
        return redirect('student_dashboard')
    elif role == 'office_staff':
        return redirect('staff_dashboard')
    elif role == 'admin':
        return redirect('admin_dashboard')
    else:
        messages.error(request, "Invalid user role. Please contact an administrator.")
        return redirect('login')


class StudentDashboardView(StudentRequiredMixin, TemplateView):
    template_name = 'appointments/student_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user_appointments = Appointment.objects.filter(student=self.request.user)

        context.update({
            'appointments': user_appointments.order_by('-created_at'),
            'pending_count': user_appointments.filter(status='pending').count(),
            'ready_count': user_appointments.filter(status='ready').count(),
            'departments': Department.objects.filter(is_active=True),
        })
        return context

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user

        context.update({
            'appointments': Appointment.objects.filter(student=user).order_by('-created_at'),
            'departments': Department.objects.filter(is_active=True),
            'pending_count': Appointment.objects.filter(student=user, status='pending').count(),
            'ready_count': Appointment.objects.filter(student=user, status='ready').count(),
        })
        return context


class OfficeStaffDashboardView(OfficeStaffRequiredMixin, TemplateView):
    template_name = 'appointments/staff_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        department = user.userprofile.department

        if department:
            appointments = Appointment.objects.filter(
                service__department=department
            ).order_by('-created_at')
        else:
            appointments = Appointment.objects.none()

        context.update({
            'appointments': appointments,
            'department': department,
            'pending_count': appointments.filter(status='pending').count(),
            'in_progress_count': appointments.filter(status='in_progress').count(),
            'ready_count': appointments.filter(status='ready').count(),
        })
        return context


class AdminDashboardView(AdminRequiredMixin, TemplateView):
    template_name = 'appointments/admin_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        context.update({
            'total_appointments': Appointment.objects.count(),
            'pending_appointments': Appointment.objects.filter(status='pending').count(),
            'ready_appointments': Appointment.objects.filter(status='ready').count(),
            'claimed_appointments': Appointment.objects.filter(status='claimed').count(),
            'departments': Department.objects.all(),
            'recent_appointments': Appointment.objects.order_by('-created_at')[:10],
            'recent_logs': AuditLog.objects.order_by('-timestamp')[:20],
        })
        return context


# Appointment Management Views
class AppointmentCreateView(StudentRequiredMixin, CreateView):
    model = Appointment
    template_name = 'appointments/appointment_create.html'
    fields = ['service', 'notes']
    success_url = reverse_lazy('student_dashboard')

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        # Only show active services
        form.fields['service'].queryset = Service.objects.filter(is_active=True).select_related('department')

        # Pre-select service if provided in URL
        service_id = self.request.GET.get('service')
        if service_id:
            try:
                service = Service.objects.get(id=service_id, is_active=True)
                form.fields['service'].initial = service
            except Service.DoesNotExist:
                pass

        return form

    def form_valid(self, form):
        form.instance.student = self.request.user
        response = super().form_valid(form)

        # Log the appointment creation
        AuditLog.objects.create(
            user=self.request.user,
            action='appointment_created',
            description=f'Created appointment for {form.instance.service.name}',
            appointment=form.instance,
            ip_address=self.get_client_ip(),
            user_agent=self.request.META.get('HTTP_USER_AGENT', '')
        )

        messages.success(self.request, f'Appointment {form.instance.appointment_id} created successfully!')
        return response

    def get_client_ip(self):
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip


class AppointmentDetailView(StudentRequiredMixin, DetailView):
    model = Appointment
    template_name = 'appointments/appointment_detail.html'
    context_object_name = 'appointment'

    def get_queryset(self):
        # Students can only view their own appointments
        return Appointment.objects.filter(student=self.request.user)


class AppointmentListView(StudentRequiredMixin, ListView):
    model = Appointment
    template_name = 'appointments/appointment_list.html'
    context_object_name = 'appointments'
    paginate_by = 10

    def get_queryset(self):
        return Appointment.objects.filter(student=self.request.user).order_by('-created_at')


class ServiceListView(StudentRequiredMixin, ListView):
    model = Service
    template_name = 'appointments/service_list.html'
    context_object_name = 'services'

    def get_queryset(self):
        department_id = self.request.GET.get('department')
        queryset = Service.objects.filter(is_active=True).select_related('department')

        if department_id:
            queryset = queryset.filter(department_id=department_id)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['departments'] = Department.objects.filter(is_active=True)
        context['selected_department'] = self.request.GET.get('department')
        return context


class ServiceDetailView(StudentRequiredMixin, DetailView):
    model = Service
    template_name = 'appointments/service_detail.html'
    context_object_name = 'service'

    def get_queryset(self):
        return Service.objects.filter(is_active=True).prefetch_related('requirements')


@method_decorator(csrf_exempt, name='dispatch')
class FileUploadView(StudentRequiredMixin, TemplateView):
    """HTMX-powered file upload for appointment requirements"""

    def post(self, request, *args, **kwargs):
        # Rate limiting check
        rate_ok, rate_message = rate_limit_check(request, 'file_upload', limit=10, window=300)
        if not rate_ok:
            return JsonResponse({
                'success': False,
                'message': rate_message
            })

        appointment_id = request.POST.get('appointment_id')
        requirement_id = request.POST.get('requirement_id')
        uploaded_file = request.FILES.get('file')

        try:
            appointment = get_object_or_404(
                Appointment,
                id=appointment_id,
                student=request.user
            )

            appointment_req = get_object_or_404(
                AppointmentRequirement,
                appointment=appointment,
                requirement_id=requirement_id
            )

            if uploaded_file:
                # Validate file security
                validation_errors = FileSecurityManager.validate_file(uploaded_file)
                if validation_errors:
                    # Log security violation
                    AuditLogger.log_security_violation(
                        request.user, 'file_access_violation',
                        f'File upload validation failed: {", ".join(validation_errors)}',
                        request, 'medium'
                    )

                    return JsonResponse({
                        'success': False,
                        'message': f'File validation failed: {", ".join(validation_errors)}'
                    })

                # Save file
                appointment_req.uploaded_file = uploaded_file
                appointment_req.save()

                # Log successful file upload
                FileSecurityManager.log_file_access(
                    user=request.user,
                    appointment=appointment,
                    file_path=appointment_req.uploaded_file.path if appointment_req.uploaded_file else '',
                    file_name=uploaded_file.name,
                    access_type='upload',
                    request=request,
                    success=True,
                    file_size=uploaded_file.size
                )

                AuditLogger.log_user_action(
                    user=request.user,
                    action='file_uploaded',
                    description=f'Uploaded file for {appointment_req.requirement.name}',
                    request=request,
                    appointment=appointment,
                    additional_data={
                        'filename': uploaded_file.name,
                        'file_size': uploaded_file.size,
                        'requirement': appointment_req.requirement.name
                    }
                )

                return JsonResponse({
                    'success': True,
                    'message': 'File uploaded successfully',
                    'filename': uploaded_file.name
                })
            else:
                return JsonResponse({
                    'success': False,
                    'message': 'No file provided'
                })

        except Exception as e:
            # Log error
            AuditLogger.log_system_event(
                action='system_error',
                description=f'File upload error: {str(e)}',
                additional_data={
                    'user': request.user.username,
                    'appointment_id': appointment_id,
                    'requirement_id': requirement_id
                }
            )

            return JsonResponse({
                'success': False,
                'message': 'An error occurred during file upload'
            })

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


# QR Code Management Views
@login_required
def generate_qr_code(request, appointment_id):
    """Generate QR code for appointment"""
    appointment = get_object_or_404(Appointment, appointment_id=appointment_id)

    # Check permissions - only student who owns appointment or staff can generate
    if not (appointment.student == request.user or
            request.user.userprofile.role in ['office_staff', 'admin']):
        raise PermissionDenied("You don't have permission to access this appointment")

    # Check if appointment is ready for QR code
    if not appointment.is_ready_for_qr:
        return JsonResponse({
            'success': False,
            'message': 'Appointment is not ready for QR code generation. Please complete all requirements first.'
        })

    try:
        qr_base64 = appointment.generate_qr_code()

        # Log QR code generation
        AuditLog.objects.create(
            user=request.user,
            action='qr_generated',
            description=f'QR code generated for appointment {appointment.appointment_id}',
            appointment=appointment,
            ip_address=get_client_ip_helper(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        return JsonResponse({
            'success': True,
            'message': 'QR code generated successfully',
            'qr_code': qr_base64,
            'appointment_id': appointment.appointment_id
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Failed to generate QR code: {str(e)}'
        })


@login_required
def download_qr_code(request, appointment_id):
    """Download QR code as PNG file"""
    appointment = get_object_or_404(Appointment, appointment_id=appointment_id)

    # Check permissions
    if not (appointment.student == request.user or
            request.user.userprofile.role in ['office_staff', 'admin']):
        raise PermissionDenied("You don't have permission to access this appointment")

    # Check if QR code exists
    if not appointment.qr_code:
        return JsonResponse({
            'success': False,
            'message': 'QR code not available for this appointment'
        })

    try:
        # Read QR code file
        with appointment.qr_code.open('rb') as qr_file:
            response = HttpResponse(qr_file.read(), content_type='image/png')
            response['Content-Disposition'] = f'attachment; filename="appointment-{appointment.appointment_id}-qr.png"'

            # Log QR code download
            AuditLog.objects.create(
                user=request.user,
                action='qr_downloaded',
                description=f'QR code downloaded for appointment {appointment.appointment_id}',
                appointment=appointment,
                ip_address=get_client_ip_helper(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

            return response

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Failed to download QR code: {str(e)}'
        })


@login_required
def validate_qr_code(request):
    """Validate scanned QR code with enhanced security"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'POST method required'})

    # Check if user is office staff or admin
    if request.user.userprofile.role not in ['office_staff', 'admin']:
        raise PermissionDenied("Only office staff can validate QR codes")

    # Rate limiting for QR scanning
    rate_ok, rate_message = rate_limit_check(request, 'qr_scan', limit=20, window=300)
    if not rate_ok:
        return JsonResponse({'success': False, 'message': rate_message})

    try:
        data = json.loads(request.body)
        scanned_data = data.get('qr_data')

        if not scanned_data:
            return JsonResponse({'success': False, 'message': 'No QR data provided'})

        # Parse QR data with additional validation
        try:
            qr_data = json.loads(scanned_data)
        except json.JSONDecodeError:
            QRSecurityManager.log_qr_scan(
                request.user, None, request, False, 'Invalid JSON in QR code'
            )
            return JsonResponse({'success': False, 'message': 'Invalid QR code format'})

        appointment_id = qr_data.get('appointment_id')
        qr_hash = qr_data.get('hash')

        if not appointment_id or not qr_hash:
            QRSecurityManager.log_qr_scan(
                request.user, None, request, False, 'Missing required QR data fields'
            )
            return JsonResponse({'success': False, 'message': 'Invalid QR code format'})

        # Get appointment
        try:
            appointment = get_object_or_404(Appointment, appointment_id=appointment_id)
        except:
            QRSecurityManager.log_qr_scan(
                request.user, None, request, False, f'Appointment not found: {appointment_id}'
            )
            return JsonResponse({'success': False, 'message': 'Appointment not found'})

        # Enhanced QR code validation with integrity check
        integrity_valid = QRSecurityManager.validate_qr_integrity(qr_data, qr_hash)
        if not integrity_valid:
            QRSecurityManager.log_qr_scan(
                request.user, appointment, request, False, 'QR code integrity check failed'
            )
            return JsonResponse({'success': False, 'message': 'QR code integrity verification failed'})

        # Validate QR code using existing method
        is_valid, message = appointment.validate_qr_code(scanned_data)

        if is_valid:
            # Log successful validation
            QRSecurityManager.log_qr_scan(
                request.user, appointment, request, True
            )

            AuditLogger.log_user_action(
                user=request.user,
                action='qr_verified',
                description=f'QR code successfully validated for appointment {appointment.appointment_id}',
                request=request,
                appointment=appointment,
                additional_data={
                    'student_name': appointment.student.get_full_name(),
                    'service': appointment.service.name,
                    'department': appointment.service.department.name
                }
            )

            return JsonResponse({
                'success': True,
                'message': message,
                'appointment': {
                    'id': appointment.appointment_id,
                    'student_name': appointment.student.get_full_name(),
                    'service': appointment.service.name,
                    'department': appointment.service.department.name,
                    'status': appointment.get_status_display(),
                    'created_at': appointment.created_at.strftime('%Y-%m-%d %H:%M')
                }
            })
        else:
            # Log failed validation
            QRSecurityManager.log_qr_scan(
                request.user, appointment, request, False, message
            )

            return JsonResponse({'success': False, 'message': message})

    except json.JSONDecodeError:
        AuditLogger.log_security_violation(
            request.user, 'qr_tampering',
            'Invalid JSON data in QR validation request',
            request, 'medium'
        )
        return JsonResponse({'success': False, 'message': 'Invalid JSON data'})
    except Exception as e:
        AuditLogger.log_system_event(
            action='system_error',
            description=f'QR validation error: {str(e)}',
            additional_data={'user': request.user.username}
        )
        return JsonResponse({'success': False, 'message': 'Validation error occurred'})


@login_required
def claim_appointment(request, appointment_id):
    """Claim appointment after QR validation"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'POST method required'})

    # Check if user is office staff or admin
    if request.user.userprofile.role not in ['office_staff', 'admin']:
        raise PermissionDenied("Only office staff can claim appointments")

    appointment = get_object_or_404(Appointment, appointment_id=appointment_id)

    try:
        success, message = appointment.claim_appointment(request.user)

        if success:
            # Log appointment claim
            AuditLog.objects.create(
                user=request.user,
                action='appointment_claimed',
                description=f'Appointment {appointment.appointment_id} claimed by {request.user.get_full_name()}',
                appointment=appointment,
                ip_address=get_client_ip_helper(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

            return JsonResponse({
                'success': True,
                'message': message,
                'appointment_status': appointment.get_status_display()
            })
        else:
            return JsonResponse({'success': False, 'message': message})

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'Claim error: {str(e)}'})


class StaffAppointmentListView(OfficeStaffRequiredMixin, ListView):
    """Staff view for managing department appointments"""
    model = Appointment
    template_name = 'appointments/staff_appointment_list.html'
    context_object_name = 'appointments'
    paginate_by = 20

    def get_queryset(self):
        user = self.request.user
        department = user.userprofile.department

        if department:
            queryset = Appointment.objects.filter(
                service__department=department
            ).select_related('student', 'service', 'service__department').prefetch_related('requirements')
        else:
            queryset = Appointment.objects.none()

        # Filter by status if provided
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        return queryset.order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        department = user.userprofile.department

        if department:
            all_appointments = Appointment.objects.filter(service__department=department)
            context.update({
                'department': department,
                'pending_count': all_appointments.filter(status='pending').count(),
                'in_progress_count': all_appointments.filter(status='in_progress').count(),
                'ready_count': all_appointments.filter(status='ready').count(),
                'claimed_count': all_appointments.filter(status='claimed').count(),
                'current_status': self.request.GET.get('status', 'all'),
            })

        return context


class StaffAppointmentDetailView(OfficeStaffRequiredMixin, DetailView):
    """Staff view for managing individual appointments"""
    model = Appointment
    template_name = 'appointments/staff_appointment_detail.html'
    context_object_name = 'appointment'

    def get_object(self, queryset=None):
        """Get appointment by appointment_id"""
        if queryset is None:
            queryset = self.get_queryset()

        appointment_id = self.kwargs.get('pk')
        try:
            return queryset.get(appointment_id=appointment_id)
        except Appointment.DoesNotExist:
            raise Http404("Appointment not found")

    def get_queryset(self):
        user = self.request.user
        department = user.userprofile.department

        if department:
            return Appointment.objects.filter(
                service__department=department
            ).select_related('student', 'service', 'service__department').prefetch_related(
                'requirements__requirement'
            )
        else:
            return Appointment.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        appointment = self.get_object()

        # Get requirements with completion status
        requirements = appointment.requirements.all().select_related('requirement')

        context.update({
            'requirements': requirements,
            'completed_requirements': requirements.filter(is_completed=True).count(),
            'total_requirements': requirements.count(),
            'can_generate_qr': appointment.is_ready_for_qr,
        })

        return context


class QRScannerView(OfficeStaffRequiredMixin, TemplateView):
    """QR Code scanner interface for office staff"""
    template_name = 'appointments/qr_scanner.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'QR Code Scanner'
        return context


@login_required
def toggle_requirement_completion(request, appointment_id, requirement_id):
    """Toggle requirement completion status (HTMX endpoint)"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'POST method required'})

    # Check if user is office staff or admin
    if request.user.userprofile.role not in ['office_staff', 'admin']:
        raise PermissionDenied("Only office staff can manage requirements")

    try:
        # Get appointment and verify department access
        appointment = get_object_or_404(Appointment, appointment_id=appointment_id)
        user_department = request.user.userprofile.department

        if user_department and appointment.service.department != user_department:
            raise PermissionDenied("You can only manage appointments from your department")

        # Get the specific requirement
        appointment_req = get_object_or_404(
            AppointmentRequirement,
            appointment=appointment,
            id=requirement_id
        )

        # Toggle completion status
        appointment_req.is_completed = not appointment_req.is_completed

        if appointment_req.is_completed:
            appointment_req.completed_by = request.user
            appointment_req.completed_at = timezone.now()
        else:
            appointment_req.completed_by = None
            appointment_req.completed_at = None

        appointment_req.save()

        # Update appointment status if all requirements are completed
        appointment.update_status()

        # Log the action
        action = 'requirement_completed' if appointment_req.is_completed else 'requirement_uncompleted'
        AuditLog.objects.create(
            user=request.user,
            action=action,
            description=f'{"Completed" if appointment_req.is_completed else "Uncompleted"} requirement: {appointment_req.requirement.name}',
            appointment=appointment,
            ip_address=get_client_ip_helper(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        return JsonResponse({
            'success': True,
            'is_completed': appointment_req.is_completed,
            'completed_by': appointment_req.completed_by.get_full_name() if appointment_req.completed_by else None,
            'completed_at': appointment_req.completed_at.strftime('%Y-%m-%d %H:%M') if appointment_req.completed_at else None,
            'appointment_status': appointment.get_status_display(),
            'can_generate_qr': appointment.is_ready_for_qr
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@login_required
def update_appointment_status(request, appointment_id):
    """Update appointment status (HTMX endpoint)"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'POST method required'})

    # Check if user is office staff or admin
    if request.user.userprofile.role not in ['office_staff', 'admin']:
        raise PermissionDenied("Only office staff can update appointment status")

    try:
        # Get appointment and verify department access
        appointment = get_object_or_404(Appointment, appointment_id=appointment_id)
        user_department = request.user.userprofile.department

        if user_department and appointment.service.department != user_department:
            raise PermissionDenied("You can only manage appointments from your department")

        data = json.loads(request.body)
        new_status = data.get('status')

        if new_status not in ['pending', 'in_progress', 'ready', 'claimed']:
            return JsonResponse({'success': False, 'message': 'Invalid status'})

        old_status = appointment.status
        appointment.status = new_status
        appointment.save()

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='status_updated',
            description=f'Updated appointment status from {old_status} to {new_status}',
            appointment=appointment,
            ip_address=get_client_ip_helper(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        return JsonResponse({
            'success': True,
            'status': appointment.get_status_display(),
            'status_value': appointment.status
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@login_required
def add_requirement_note(request, appointment_id, requirement_id):
    """Add note to requirement (HTMX endpoint)"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'POST method required'})

    # Check if user is office staff or admin
    if request.user.userprofile.role not in ['office_staff', 'admin']:
        raise PermissionDenied("Only office staff can add notes")

    try:
        # Get appointment and verify department access
        appointment = get_object_or_404(Appointment, appointment_id=appointment_id)
        user_department = request.user.userprofile.department

        if user_department and appointment.service.department != user_department:
            raise PermissionDenied("You can only manage appointments from your department")

        # Get the specific requirement
        appointment_req = get_object_or_404(
            AppointmentRequirement,
            appointment=appointment,
            id=requirement_id
        )

        data = json.loads(request.body)
        note = data.get('note', '').strip()

        if note:
            appointment_req.notes = note
            appointment_req.save()

            # Log the action
            AuditLog.objects.create(
                user=request.user,
                action='note_added',
                description=f'Added note to requirement: {appointment_req.requirement.name}',
                appointment=appointment,
                ip_address=get_client_ip_helper(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

        return JsonResponse({
            'success': True,
            'note': appointment_req.notes
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


def get_client_ip_helper(request):
    """Helper function to get client IP address"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


# ============================================================================
# ADMINISTRATOR VIEWS
# ============================================================================

class AdminRequiredMixin(LoginRequiredMixin):
    """Mixin to require admin role for access"""

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()

        if not hasattr(request.user, 'userprofile') or request.user.userprofile.role != 'admin':
            raise PermissionDenied("Admin access required")

        return super().dispatch(request, *args, **kwargs)


class AdminDashboardView(AdminRequiredMixin, TemplateView):
    """Administrator dashboard with system overview"""
    template_name = 'appointments/admin_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get all appointments across departments
        all_appointments = Appointment.objects.all().select_related(
            'student', 'service', 'service__department'
        )

        # Department statistics
        departments = Department.objects.all()
        dept_stats = []
        for dept in departments:
            dept_appointments = all_appointments.filter(service__department=dept)
            dept_stats.append({
                'department': dept,
                'total_appointments': dept_appointments.count(),
                'pending': dept_appointments.filter(status='pending').count(),
                'in_progress': dept_appointments.filter(status='in_progress').count(),
                'ready': dept_appointments.filter(status='ready').count(),
                'claimed': dept_appointments.filter(status='claimed').count(),
            })

        # Overall system statistics
        total_users = User.objects.count()
        total_students = User.objects.filter(userprofile__role='student').count()
        total_staff = User.objects.filter(userprofile__role='office_staff').count()
        total_admins = User.objects.filter(userprofile__role='admin').count()

        # Recent activity (last 10 appointments)
        recent_appointments = all_appointments.order_by('-created_at')[:10]

        context.update({
            'department_stats': dept_stats,
            'total_appointments': all_appointments.count(),
            'pending_appointments': all_appointments.filter(status='pending').count(),
            'in_progress_appointments': all_appointments.filter(status='in_progress').count(),
            'ready_appointments': all_appointments.filter(status='ready').count(),
            'claimed_appointments': all_appointments.filter(status='claimed').count(),
            'total_users': total_users,
            'total_students': total_students,
            'total_staff': total_staff,
            'total_admins': total_admins,
            'recent_appointments': recent_appointments,
            'departments': departments,
        })

        return context


class AdminAppointmentListView(AdminRequiredMixin, ListView):
    """Admin view for managing all appointments across departments"""
    model = Appointment
    template_name = 'appointments/admin_appointment_list.html'
    context_object_name = 'appointments'
    paginate_by = 25

    def get_queryset(self):
        queryset = Appointment.objects.all().select_related(
            'student', 'service', 'service__department'
        ).prefetch_related('requirements')

        # Filter by department if provided
        department_id = self.request.GET.get('department')
        if department_id:
            queryset = queryset.filter(service__department_id=department_id)

        # Filter by status if provided
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Search by student name or appointment ID
        search = self.request.GET.get('search')
        if search:
            from django.db.models import Q
            queryset = queryset.filter(
                Q(student__first_name__icontains=search) |
                Q(student__last_name__icontains=search) |
                Q(appointment_id__icontains=search)
            )

        return queryset.order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Add filter options
        context.update({
            'departments': Department.objects.all(),
            'current_department': self.request.GET.get('department', ''),
            'current_status': self.request.GET.get('status', ''),
            'current_search': self.request.GET.get('search', ''),
        })

        return context


class AdminUserListView(AdminRequiredMixin, ListView):
    """Admin view for managing all users"""
    model = User
    template_name = 'appointments/admin_user_list.html'
    context_object_name = 'users'
    paginate_by = 25

    def get_queryset(self):
        queryset = User.objects.all().select_related('userprofile')

        # Filter by role if provided
        role = self.request.GET.get('role')
        if role:
            queryset = queryset.filter(userprofile__role=role)

        # Search by name or email
        search = self.request.GET.get('search')
        if search:
            from django.db.models import Q
            queryset = queryset.filter(
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(email__icontains=search) |
                Q(username__icontains=search)
            )

        return queryset.order_by('last_name', 'first_name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Add filter options and statistics
        context.update({
            'current_role': self.request.GET.get('role', ''),
            'current_search': self.request.GET.get('search', ''),
            'total_students': User.objects.filter(userprofile__role='student').count(),
            'total_staff': User.objects.filter(userprofile__role='office_staff').count(),
            'total_admins': User.objects.filter(userprofile__role='admin').count(),
        })

        return context


class AdminDepartmentListView(AdminRequiredMixin, ListView):
    """Admin view for managing departments and services"""
    model = Department
    template_name = 'appointments/admin_department_list.html'
    context_object_name = 'departments'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Add service statistics for each department
        departments_with_stats = []
        for dept in context['departments']:
            services = dept.services.all()
            appointments = Appointment.objects.filter(service__department=dept)

            departments_with_stats.append({
                'department': dept,
                'services_count': services.count(),
                'appointments_count': appointments.count(),
                'pending_count': appointments.filter(status='pending').count(),
                'staff_count': User.objects.filter(userprofile__department=dept).count(),
            })

        context['departments_with_stats'] = departments_with_stats
        return context


class AdminSystemReportsView(AdminRequiredMixin, TemplateView):
    """Admin view for system reports and analytics"""
    template_name = 'appointments/admin_reports.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Date range filtering
        from datetime import datetime, timedelta
        from django.utils import timezone

        end_date = timezone.now()
        start_date = end_date - timedelta(days=30)  # Last 30 days by default

        # Get date range from request if provided
        if self.request.GET.get('start_date'):
            start_date = datetime.strptime(self.request.GET.get('start_date'), '%Y-%m-%d')
            start_date = timezone.make_aware(start_date)

        if self.request.GET.get('end_date'):
            end_date = datetime.strptime(self.request.GET.get('end_date'), '%Y-%m-%d')
            end_date = timezone.make_aware(end_date)

        # Filter appointments by date range
        appointments = Appointment.objects.filter(
            created_at__range=[start_date, end_date]
        ).select_related('student', 'service', 'service__department')

        # Generate statistics
        total_appointments = appointments.count()
        completed_appointments = appointments.filter(status='claimed').count()

        # Department breakdown
        dept_breakdown = []
        for dept in Department.objects.all():
            dept_appointments = appointments.filter(service__department=dept)
            dept_breakdown.append({
                'department': dept,
                'total': dept_appointments.count(),
                'pending': dept_appointments.filter(status='pending').count(),
                'in_progress': dept_appointments.filter(status='in_progress').count(),
                'ready': dept_appointments.filter(status='ready').count(),
                'claimed': dept_appointments.filter(status='claimed').count(),
            })

        # Daily appointment counts for chart
        daily_counts = []
        current_date = start_date.date()
        while current_date <= end_date.date():
            count = appointments.filter(created_at__date=current_date).count()
            daily_counts.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'count': count
            })
            current_date += timedelta(days=1)

        context.update({
            'start_date': start_date.date(),
            'end_date': end_date.date(),
            'total_appointments': total_appointments,
            'completed_appointments': completed_appointments,
            'completion_rate': (completed_appointments / total_appointments * 100) if total_appointments > 0 else 0,
            'dept_breakdown': dept_breakdown,
            'daily_counts': daily_counts,
        })

        return context


class AdminSecurityDashboardView(AdminRequiredMixin, TemplateView):
    """Security dashboard for administrators"""
    template_name = 'appointments/admin_security_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get security event counts by severity
        now = timezone.now()
        week_ago = now - timedelta(days=7)
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)

        context['critical_events'] = SecurityEvent.objects.filter(
            severity='critical', resolved=False
        ).count()

        context['high_events'] = SecurityEvent.objects.filter(
            severity='high', resolved=False
        ).count()

        context['resolved_today'] = SecurityEvent.objects.filter(
            resolved=True, resolved_at__gte=today_start
        ).count()

        context['total_events_week'] = SecurityEvent.objects.filter(
            timestamp__gte=week_ago
        ).count()

        # Recent security events
        context['recent_events'] = SecurityEvent.objects.filter(
            resolved=False
        ).order_by('-timestamp')[:20]

        # Event types breakdown
        event_types = SecurityEvent.objects.filter(
            timestamp__gte=week_ago
        ).values('event_type').annotate(
            count=Count('event_type')
        ).order_by('-count')

        total_events = sum(et['count'] for et in event_types)
        for et in event_types:
            et['percentage'] = (et['count'] / total_events * 100) if total_events > 0 else 0

        context['event_types'] = event_types

        # Top IP addresses with security events
        top_ips = SecurityEvent.objects.filter(
            timestamp__gte=week_ago
        ).values('ip_address').annotate(
            count=Count('ip_address')
        ).order_by('-count')[:10]

        max_ip_count = max([ip['count'] for ip in top_ips]) if top_ips else 1
        for ip in top_ips:
            ip['percentage'] = (ip['count'] / max_ip_count * 100) if max_ip_count > 0 else 0

        context['top_ips'] = top_ips
        context['page_title'] = 'Security Dashboard'

        return context


@login_required
def resolve_security_event(request, event_id):
    """Resolve a security event"""
    if request.user.userprofile.role != 'admin':
        raise PermissionDenied("Only administrators can resolve security events")

    if request.method == 'POST':
        event = get_object_or_404(SecurityEvent, id=event_id)
        event.resolved = True
        event.resolved_by = request.user
        event.resolved_at = timezone.now()
        event.save()

        # Log the resolution
        AuditLogger.log_user_action(
            user=request.user,
            action='security_event_resolved',
            description=f'Resolved security event: {event.event_type}',
            request=request,
            additional_data={'event_id': event_id, 'event_type': event.event_type}
        )

        return JsonResponse({'success': True, 'message': 'Security event resolved'})

    return JsonResponse({'success': False, 'message': 'Invalid request method'})


@login_required
def resolve_all_low_events(request):
    """Resolve all low severity security events"""
    if request.user.userprofile.role != 'admin':
        raise PermissionDenied("Only administrators can resolve security events")

    if request.method == 'POST':
        count = SecurityEvent.objects.filter(
            severity='low', resolved=False
        ).update(
            resolved=True,
            resolved_by=request.user,
            resolved_at=timezone.now()
        )

        # Log the bulk resolution
        AuditLogger.log_user_action(
            user=request.user,
            action='bulk_security_resolution',
            description=f'Resolved {count} low severity security events',
            request=request,
            additional_data={'count': count, 'severity': 'low'}
        )

        return JsonResponse({'success': True, 'message': f'Resolved {count} low severity events'})

    return JsonResponse({'success': False, 'message': 'Invalid request method'})


@login_required
def export_security_report(request):
    """Export security report as CSV"""
    if request.user.userprofile.role != 'admin':
        raise PermissionDenied("Only administrators can export security reports")

    # Log the export
    AuditLogger.log_user_action(
        user=request.user,
        action='data_export',
        description='Exported security report',
        request=request,
        additional_data={'export_type': 'security_report'}
    )

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="security_report_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'

    writer = csv.writer(response)
    writer.writerow([
        'Timestamp', 'Event Type', 'Severity', 'User', 'IP Address',
        'Description', 'Resolved', 'Resolved By', 'Resolved At'
    ])

    # Get security events from last 30 days
    thirty_days_ago = timezone.now() - timedelta(days=30)
    events = SecurityEvent.objects.filter(
        timestamp__gte=thirty_days_ago
    ).order_by('-timestamp')

    for event in events:
        writer.writerow([
            event.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            event.get_event_type_display(),
            event.get_severity_display(),
            event.user.username if event.user else 'Anonymous',
            event.ip_address,
            event.description,
            'Yes' if event.resolved else 'No',
            event.resolved_by.username if event.resolved_by else '',
            event.resolved_at.strftime('%Y-%m-%d %H:%M:%S') if event.resolved_at else ''
        ])

    return response


class AdminAuditLogsView(AdminRequiredMixin, ListView):
    """View audit logs for administrators"""
    model = AuditLog
    template_name = 'appointments/admin_audit_logs.html'
    context_object_name = 'audit_logs'
    paginate_by = 50

    def get_queryset(self):
        queryset = AuditLog.objects.all().order_by('-timestamp')

        # Filter by action if specified
        action = self.request.GET.get('action')
        if action:
            queryset = queryset.filter(action=action)

        # Filter by user if specified
        user = self.request.GET.get('user')
        if user:
            queryset = queryset.filter(user__username__icontains=user)

        # Filter by date range if specified
        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')

        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                queryset = queryset.filter(timestamp__date__gte=start_date)
            except ValueError:
                pass

        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                queryset = queryset.filter(timestamp__date__lte=end_date)
            except ValueError:
                pass

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Audit Logs'
        context['actions'] = AuditLog.ACTION_CHOICES
        context['current_filters'] = {
            'action': self.request.GET.get('action', ''),
            'user': self.request.GET.get('user', ''),
            'start_date': self.request.GET.get('start_date', ''),
            'end_date': self.request.GET.get('end_date', ''),
        }
        return context
