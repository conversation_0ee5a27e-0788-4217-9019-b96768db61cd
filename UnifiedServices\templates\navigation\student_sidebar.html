{% comment %}
Student Sidebar Navigation Component
Reusable sidebar navigation for student role with emerald/gold theme
{% endcomment %}

{% load url %}

{% comment %} Mobile Navigation {% endcomment %}
{% block student_mobile_nav %}
<ul role="list" class="flex flex-1 flex-col gap-y-7">
    <li>
        <div class="text-xs font-semibold leading-6 text-emerald-600 mb-2">STUDENT PORTAL</div>
        <ul role="list" class="-mx-2 space-y-1">
            <li>
                <a href="{% url 'student_dashboard' %}"
                   class="{% if request.resolver_match.url_name == 'student_dashboard' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'student_dashboard' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                        </svg>
                    </div>
                    Dashboard
                </a>
            </li>
            <li>
                <a href="{% url 'appointment_list' %}"
                   class="{% if request.resolver_match.url_name == 'appointment_list' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'appointment_list' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5a2.25 2.25 0 012.25 2.25v7.5" />
                        </svg>
                    </div>
                    My Appointments
                </a>
            </li>
            <li>
                <a href="{% url 'service_list' %}"
                   class="{% if request.resolver_match.url_name == 'service_list' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'service_list' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
                        </svg>
                    </div>
                    Available Services
                </a>
            </li>
            <li>
                <a href="{% url 'appointment_create' %}"
                   class="{% if request.resolver_match.url_name == 'appointment_create' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'appointment_create' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                        </svg>
                    </div>
                    New Appointment
                </a>
            </li>
        </ul>
    </li>
    
    <li>
        <div class="text-xs font-semibold leading-6 text-gray-400 mb-2">ACCOUNT</div>
        <ul role="list" class="-mx-2 space-y-1">
            <li>
                <a href="#"
                   class="text-gray-700 hover:text-emerald-600 hover:bg-emerald-50 group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                        </svg>
                    </div>
                    Profile
                </a>
            </li>
            <li>
                <a href="#"
                   class="text-gray-700 hover:text-emerald-600 hover:bg-emerald-50 group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
                        </svg>
                    </div>
                    Help & Support
                </a>
            </li>
        </ul>
    </li>
</ul>
{% endblock %}

{% comment %} Desktop Navigation {% endcomment %}
{% block student_desktop_nav %}
<ul role="list" class="flex flex-1 flex-col gap-y-7">
    <li>
        <div class="text-xs font-semibold leading-6 text-emerald-600 mb-2">STUDENT PORTAL</div>
        <ul role="list" class="-mx-2 space-y-1">
            <li>
                <a href="{% url 'student_dashboard' %}"
                   class="{% if request.resolver_match.url_name == 'student_dashboard' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'student_dashboard' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                        </svg>
                    </div>
                    Dashboard
                </a>
            </li>
            <li>
                <a href="{% url 'appointment_list' %}"
                   class="{% if request.resolver_match.url_name == 'appointment_list' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'appointment_list' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5a2.25 2.25 0 012.25 2.25v7.5" />
                        </svg>
                    </div>
                    My Appointments
                </a>
            </li>
            <li>
                <a href="{% url 'service_list' %}"
                   class="{% if request.resolver_match.url_name == 'service_list' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'service_list' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
                        </svg>
                    </div>
                    Available Services
                </a>
            </li>
            <li>
                <a href="{% url 'appointment_create' %}"
                   class="{% if request.resolver_match.url_name == 'appointment_create' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'appointment_create' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                        </svg>
                    </div>
                    New Appointment
                </a>
            </li>
        </ul>
    </li>
    
    <li>
        <div class="text-xs font-semibold leading-6 text-gray-400 mb-2">ACCOUNT</div>
        <ul role="list" class="-mx-2 space-y-1">
            <li>
                <a href="#"
                   class="text-gray-700 hover:text-emerald-600 hover:bg-emerald-50 group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                        </svg>
                    </div>
                    Profile
                </a>
            </li>
            <li>
                <a href="#"
                   class="text-gray-700 hover:text-emerald-600 hover:bg-emerald-50 group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
                        </svg>
                    </div>
                    Help & Support
                </a>
            </li>
        </ul>
    </li>
</ul>
{% endblock %}
