{% comment %}
Student Navigation Component
This component provides consistent navigation for student users across all pages.
{% endcomment %}

<ul role="list" class="flex flex-1 flex-col gap-y-7">
    <li>
        <div class="text-xs font-semibold leading-6 text-emerald-600 mb-2">STUDENT PORTAL</div>
        <ul role="list" class="-mx-2 space-y-1">
            <li>
                <a href="{% url 'student_dashboard' %}"
                   class="{% if request.resolver_match.url_name == 'student_dashboard' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200"
                   up-target="main"
                   up-history="true">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'student_dashboard' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                        </svg>
                    </div>
                    Dashboard
                </a>
            </li>
            <li>
                <a href="{% url 'appointment_create' %}"
                   class="{% if request.resolver_match.url_name == 'appointment_create' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200"
                   up-target="main"
                   up-history="true">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'appointment_create' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                        </svg>
                    </div>
                    New Appointment
                </a>
            </li>
            <li>
                <a href="{% url 'appointment_list' %}"
                   class="{% if request.resolver_match.url_name == 'appointment_list' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200"
                   up-target="main"
                   up-history="true">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'appointment_list' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 17.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                        </svg>
                    </div>
                    My Appointments
                </a>
            </li>
            <li>
                <a href="{% url 'service_list' %}"
                   class="{% if request.resolver_match.url_name == 'service_list' %}bg-emerald-50 text-emerald-700{% else %}text-gray-700 hover:text-emerald-600 hover:bg-emerald-50{% endif %} group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200"
                   up-target="main"
                   up-history="true">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg {% if request.resolver_match.url_name == 'service_list' %}bg-emerald-600 text-white{% else %}border border-gray-200 bg-white text-gray-400 group-hover:border-emerald-300 group-hover:text-emerald-600{% endif %}">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                    Browse Services
                </a>
            </li>
        </ul>
    </li>
    <li class="mt-auto">
        <div class="text-xs font-semibold leading-6 text-gray-400 mb-2">QUICK ACTIONS</div>
        <ul role="list" class="-mx-2 space-y-1">
            <li>
                <a href="#" class="text-gray-700 hover:text-gold-600 hover:bg-gold-50 group flex gap-x-3 rounded-lg p-3 text-sm leading-6 font-semibold transition-all duration-200">
                    <div class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border border-gray-200 bg-white text-gray-400 group-hover:border-gold-300 group-hover:text-gold-600">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
                        </svg>
                    </div>
                    Help & Support
                </a>
            </li>
        </ul>
    </li>
</ul>
